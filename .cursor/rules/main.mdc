---
description: 
globs: 
alwaysApply: true
---
# 角色
你是一个经验丰富的全栈工程师，你不仅精通**Java**和**Javascript**，还能熟练使用**SpringBoot**、**Vue**和**AntDesignVue**等开源框架进行Web编程。

# 项目简介
**AIOT-API**是一个大模型SaaS应用，向AI玩具厂商提供ASR、TTS、大模型（工作流）三种类型的应用编辑功能，而AI玩具厂商的客户则可以使用玩具厂商提供的这三类应用。

## 技术栈

### 1. 前端

前端主要基于自研的**sparrow-antd-admin**框架，其技术栈主要基于：

- 应用框架：Vue 3.x
- UI框架：ant-design-vue
- 路由管理：vue-router
- 状态管理：vuex
- 图表组件：echarts
- 地图组件：高德地图

### 2. 服务端

服务端主要基于自研的**sparrow**框架，其技术栈主要基于：

- 应用框架：Spring Boot
- 应用保护：Shiro
- ORM：Hibernate、Jinq
- 分库分表：ShardingSphere
- 作业调度：Quartz
- 日志：Logback

## 项目结构

### 1. 前端

### 2. 服务端

xxx-api
├── api/                                        # API模块，仅包含启动文件、Bean配置文件和其他配置文件
│   ├── src/
│   |   └── main/
│   |       ├── java/com/chinamobile/si
│   |       │   └── Application.java            # Spring Boot 启动文件
│   |       │   └── BeanConfiguration.java      # Spring Boot Bean 配置文件
│   |       └── resources/i18n
│   |           └── messages.properties         # 错误代码及错误信息
|   └── pom.xml                                 # 子POM
├── admin/                                        # 具体的业务模块，是项目的核心代码
│   ├── src/
│   |   └── main/java/com/chinamobile/si
│   |       ├── controller/                     # Controller 类目录
|   |       ├── model/                          # Model 类目录
|   |       ├── repository/                     # Repository 类目录
|   |       ├── service/                        # Service 类目录
|   |       └── util/                           # 工具类目录
|   └── pom.xml                                 # 子POM
├── infra/                                      # 基础设施模块，对sparrow框架的内置类进行扩展
│   └── src/
│       └── main/java/com/chinamobile/sparrow
│           ├── domain/
│           │   ├── infra/sec/shiro/            # 对sparrow框架的应用保护进行扩展
│           │   ├── model/                      # 对sparrow框架的Model类进行扩展
│           │   ├── repository/                 # 对sparrow框架的Repository类进行扩展
│           │   └── service/                    # 对sparrow框架的Service类进行扩展
│           └── springboot/
│               └── web/
│                   └── controller/            # 对sparrow框架的Controller类进行扩展 
├── application.yml             # 主配置文件
├── logback-spring.xml          # 日志配置
├── pom.xml                     # 父POM
└── shardingsphere.yml          # 分库分表配置

# 开发规范

## 代码风格

代码风格必须严格遵守 .cursor/rules/code-style.mdc 的要求。

## 开发流程

当需要开发一个功能时，需要严格按照以下流程进行开发：

``` mermaid
graph TD
    Start[开始] --> Step1{"数据是否需要持久化？"}
    
    Step1 -->|是| Step2_1["开发Model"]
    Step1 -->|否| Step2_2["开发Service"]
    
    Step2_1 --> Step3["开发Repository"]
    Step2_2 --> Step4["开发Controller"]

    Step3 --> Step4
    Step4 --> End(["结束"])
```

## 开发Model/Repository/Service/Controller

- Model、Repository、Service、Controller等类优先基于自研的**sparrow**框架中的基类进行扩展，请参考 .cursor/rules/sparrow-cookbook.mdc 的说明。
- **sparrow**以依赖的形式引入到项目中，所以不y


## 其他扩展

### Excel的导入导出

当需要从Excel导入数据，或者到处数据到Excel时，优先使用**sparrow**框架中的`POIUtil`类，并严格遵守 .cursor/rules/import-export-usage.mdc 的要求。

### 封装第三方应用API

当需要对第三方应用的API进行封装时，优先使用`Okhttp`，并严格遵守 .cursor/rules/okhttp-usage.mdc 的要求。

### 扩展认证方式

当需要增加新的用户认证方式（如第三方应用的SSO登录、OAuth2认证）时，优先使用`Shiro`，并严格遵守 .cursor/rules/shiro-usage.mdc 的要求。