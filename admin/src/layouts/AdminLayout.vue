<script setup>
import { nextTick, provide, ref } from 'vue'
import { Grid } from 'ant-design-vue'
import SidebarMenu from '@/components/SidebarMenu.vue'
import FeedbackUtil from '@/utils/feedback.js'
import AppApi from '@/api/sys/app.js'
import LoginApi from '@/api/sec/login.js'

const sidebar = ref({
  open: true
})
provide('sidebar', sidebar)

// 小屏默认关闭侧边栏
const breakpoint = Grid.useBreakpoint()
nextTick(() => {
  sidebar.value.open = breakpoint.value.xs !== true
})

// 折叠菜单
const collapseSidebar = () => {
  sidebar.value.open = !sidebar.value.open
}

// 加载用户信息
const user = ref(null)
AppApi.getUser({
  showLoading: false,
  toast: {
    success: false,
    error: false
  }
}).then(result => {
  user.value = result.data
})

const menu = AppApi.getPages({
  showLoading: false,
  toast: {
    success: false
  }
})

// 注销登录
const logout = () => {
  FeedbackUtil.modal('您即将注销登录，是否继续？', 'confirm', {
    onOk () {
      LoginApi.logout({
        toast: {
          success: false
        }
      }).then(() => {
        FeedbackUtil.message('注销成功，系统即将为您重定向……', 'success', {
          duration: 1,
          onClose () {
            FeedbackUtil.redirectToLogin()
          }
        })
      })
    }
  })
}
</script>

<template>
  <a-layout class="admin-layout">
    <!-- 固定顶部栏 -->
    <a-affix>
      <a-layout-header>
        <div>
          <!-- logo -->
          <div class="layout-header-logo">
            <img
              src="@/assets/logo.png"
              alt=""
            >
          </div>

          <!-- 菜单导航按钮 -->
          <div class="layout-sider-trigger">
            <template v-if="sidebar.open">
              <menu-fold-outlined @click="collapseSidebar" />
            </template>
            <template v-else>
              <menu-unfold-outlined @click="collapseSidebar" />
            </template>
          </div>
        </div>

        <a-dropdown class="layout-header-action">
          <div style="display: flex;align-items: center">
            <!-- 头像 -->
            <template v-if="user && user.avatarUrl">
              <a-avatar
                :size="'small'"
                :src="user.avatarUrl"
              />
            </template>
            <template v-else>
              <a-avatar :size="'small'">
                <template #icon>
                  <user-outlined />
                </template>
              </a-avatar>
            </template>

            <!-- 用户名 -->
            <span v-if="user !== null">
              {{ user.name }}
            </span>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="logout">
                <logout-outlined />
                注销
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-layout-header>
    </a-affix>

    <a-layout>
      <!-- 小屏使用抽屉+侧边栏展示菜单 -->
      <template v-if="breakpoint.xs === true">
        <a-drawer
          v-model:open="sidebar.open"
          :body-style="{ backgroundColor: '#001529', padding: 0 }"
          :closable="false"
          :mask="true"
          :mask-closable="true"
          :placement="'left'"
          :width="'200px'"
        >
          <SidebarMenu
            :collapsed="false"
            :menu="menu"
            :trigger="null"
          />
        </a-drawer>
      </template>
      <template v-else>
        <!-- 大屏单独使用侧边栏展示菜单 -->
        <div>
          <SidebarMenu
            :collapsed="!sidebar.open"
            :menu="menu"
            :trigger="null"
          />
        </div>
      </template>

      <div class="layout-content">
        <slot name="header" />

        <!-- 内容 -->
        <a-layout-content>
          <slot name="body" />
        </a-layout-content>
      </div>
    </a-layout>

    <!-- 底部 -->
    <!-- <a-layout-footer /> -->
  </a-layout>
</template>

<style lang="less" scoped>
@import '@/less/default';

.admin-layout {
  min-height: 100vh;

  .ant-layout-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgb(0 21 41 / 8%);
    padding: 0;
    background: #fff;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);

    > div:first-child {
      display: flex;

      .layout-sider-trigger .anticon {
        padding: 0 @padding-lg;
        font-size: 18px;
        line-height: 64px;
        cursor: pointer;
      }
    }

    .layout-header-action {
      padding: 0 @padding-sm;
      cursor: pointer;

      &:hover {
        background: rgba(0, 0, 0, .025);
      }

      .ant-avatar {
        margin-right: @margin-xs;
      }
    }
  }

  .ant-layout-sider {
    height: 100%;
  }

  .layout-content {
    margin: 0;
    width: 100%;
    overflow: auto;

    .ant-layout-content {
      padding: @padding-sm;
      min-height: calc(100vh - 128px);
    }
  }

  .ant-layout-footer {
    text-align: center
  }
}

/* 大屏Logo */
@media (min-width: @screen-sm) {
  .layout-header-logo {
    display: flex;
    align-items: center;
    margin: 0 @margin-md;
    width: 168px;

    > img {
      width: 100%;
    }
  }
}

/* 小屏Logo */
@media (max-width: @screen-xs-max) {
  .layout-header-logo {
    display: none;
  }
}
</style>
