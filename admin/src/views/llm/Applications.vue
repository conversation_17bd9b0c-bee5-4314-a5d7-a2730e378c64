<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import ApplicationApi from '@/api/application.js'
import FeedbackUtil from '@/utils/feedback.js'
import GenericIcon from '@/components/GenericIcon.vue'

const { proxy } = getCurrentInstance()

const records = ref([])

const load = () => {
  ApplicationApi.search(-1, -1, null, null, {
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data.records
  })
}

const enter = record => {
  ApplicationApi.installId(record.appId, {
    toast: {
      success: false
    }
  }).then(result => {
    proxy.$router.push({
      name: 'llm.application.home',
      query: {
        id: result.data
      }
    })
  })
}

const edit = record => {
  proxy.$router.push({
    name: 'llm.application.edit',
    query: record === null
      ? {}
      : {
          id: record.appId
        }
  })
}

const remove = id => {
  FeedbackUtil.modal('您即将删除该记录，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = ApplicationApi.remove(id, {
        showLoading: false
      })
      _promise.then(() => {
        load()
      })

      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <a-row :gutter="[16, 16]">
    <a-col
      :xs="24"
      :md="8"
      :lg="6"
    >
      <a-card
        :hoverable="true"
        @click="edit(null);"
      >
        <a-card-meta
          :title="'新建应用'"
          :description="'创建一个新的工作流应用'"
        >
          <template #avatar>
            <a-avatar style="background-color: #1890ff;">
              <plus-outlined />
            </a-avatar>
          </template>
        </a-card-meta>
      </a-card>
    </a-col>

    <a-col
      v-for="i in records"
      :key="i.id"
      :xs="24"
      :sm="8"
      :md="6"
    >
      <a-card :hoverable="true">
        <a-card-meta
          :title="i.name || '未命名'"
          :description="i.description || '暂无描述'"
        >
          <template #avatar>
            <a-avatar style="background-color: #fa8c16;">
              <GenericIcon :icon="'icon-robot-2-line'" />
            </a-avatar>
          </template>
        </a-card-meta>

        <template #actions>
          <a-tooltip :title="'对话'">
            <comment-outlined @click="enter(i);" />
          </a-tooltip>
          <a-tooltip :title="'编辑'">
            <edit-outlined @click="edit(i);" />
          </a-tooltip>
          <a-tooltip :title="'删除'">
            <delete-outlined @click="remove(i.id);" />
          </a-tooltip>
        </template>
      </a-card>
    </a-col>
  </a-row>
</template>

<style lang="less" scoped>
.ant-card {
  height: 100%;
}
</style>
