<script setup>
import { getCurrentInstance, inject, onMounted, ref, watch } from 'vue'
import App from '@/components/llm/App.vue'
import ConversationApi from '@/api/llm/conversation.js'
import MediaApi from '@/api/media/record.js'
import ModelApi from '@/api/llm/model.js'
import PluginApi from '@/api/llm/plugin.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const route = proxy.$route.name

const models = ref({
  default: [],
  reasoning: []
})

const questions = ref([{
  icon: 'AccountOutlined',
  desc: '请介绍一下你自己'
}])

const conversation = ref({
  id: '',
  title: ''
})

const getConversationId = () => {
  return new Promise(resolve => {
    if (conversation.value.id !== '') {
      resolve()
      return
    }

    // 新建对话
    ConversationApi.create('', {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      conversation.value.id = result.data
      resolve()
    })
  })
}

const messages = ref([])

const goto = conversation => {
  proxy.$router.push({
    query: {
      id: conversation.id
    }
  })
}

watch(
  () => proxy.$route.query.id,
  val => {
    if (route === proxy.$route.name) {
      init(val)
    }
  }
)

// 对话
const findConversations = () => {
  return new Promise(resolve => {
    ConversationApi.me({
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    })
  })
}

const newConversation = () => {
  return ConversationApi.create(conversation.value.id, {
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    if (result.data === conversation.value.id) {
      FeedbackUtil.message('当前已是最新对话')
    } else {
      proxy.$router.push({
        query: {
          id: result.data
        }
      })
    }
  })
}

const renameConversation = (conversation, title) => {
  return ConversationApi.rename(conversation.id, title, {
    toast: {
      success: false
    }
  })
}

const reloadPage = inject('reloadPage')
const removeConversation = conv => {
  const _promise = ConversationApi.remove(conv.id, {
    toast: {
      success: false
    }
  })
  _promise.then(() => {
    // 删除的是当前会话
    if (conversation.value.id === conv.id) {
      if (proxy.$route.query.id) {
        proxy.$router.push({
          name: proxy.$route.name
        })
      } else {
        reloadPage()
      }
    }
  })

  return _promise
}

// 记忆
const clearMemory = () => {
  return conversation.value.id === ''
    ? Promise.resolve()
    : ConversationApi.clearMemory(conversation.value.id, {
      toast: {
        success: false
      }
    })
}

const send = (modelName, plugins, mediaId, message, onopen, onmessage, onclose, onerror) => {
  return getConversationId().then(() => {
    return ConversationApi.generate(conversation.value.id, modelName, plugins, null, mediaId, message, event => {
      onopen(event)
    }, event => {
      onmessage(event)
    }, event => {
      onclose(event)
    }, error => {
      onerror(error)
    })
  })
}

const uploadMedia = file => {
  return MediaApi.uploadForm(null, [file], {
    showLoading: false,
    toast: {
      success: false
    }
  })
}

const removeMedia = file => {
  return MediaApi.remove(file.id, false, {
    showLoading: false,
    toast: {
      success: false
    }
  })
}

const documents = ref([])

const uploadDocument = file => {
  return getConversationId().then(() => {
    return ConversationApi.uploadDocument(conversation.value.id, 'conversation', [file], {
      showLoading: false,
      toast: {
        success: false
      }
    })
  })
}

const removeDocument = file => {
  return ConversationApi.removeDocument(file.id, {
    showLoading: false,
    toast: {
      success: false
    }
  })
}

const init = conversationId => {
  conversation.value.id = conversationId || ''

  if (conversation.value.id === '') {
    conversation.value.title = ''
    documents.value = []
    messages.value = []
  } else {
    ConversationApi.get(conversation.value.id, {
      toast: {
        success: false
      }
    }).then(result => {
      conversation.value.title = result.data.title
    })

    // 加载知识库
    ConversationApi.findDocuments(conversation.value.id, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      documents.value = result.data.map(i => i.document)
    })

    // 加载对话记忆
    ConversationApi.memories(conversation.value.id, {
      toast: {
        success: false
      }
    }).then(result => {
      messages.value = result.data.map(i => {
        return i.userId
          ? {
              id: i.id,
              type: 'ask',
              content: i.message,
              mediaURL: i.mediaId ? MediaApi.preview(i.mediaId) : null,
              timestamp: new Date(i.timestamp),
              status: 'success'
            }
          : {
              id: i.id,
              type: 'answer',
              reasoning: i.reasoning,
              content: i.message,
              timestamp: new Date(i.timestamp),
              status: 'success'
            }
      })
    })
  }
}

const accept = ref('')

const speechConfig = ref(null)

onMounted(() => {
  init(proxy.$route.query.id)

  // 加载大模型
  ModelApi.find({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    models.value = {
      default: result.data
        .filter(i => i.mode === 'DEFAULT')
        .map(i => {
          return {
            title: i.title,
            name: i.name
          }
        }),
      reasoning: result.data
        .filter(i => i.mode === 'REASONING')
        .map(i => {
          return {
            title: i.title,
            name: i.name
          }
        })
    }
  })

  ConversationApi.accept({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    accept.value = result.data
  })

  PluginApi.speechKey({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    if (typeof result.data[0] === 'string' && result.data[0].length > 0 && typeof result.data[1] === 'string' && result.data[1].length > 0) {
      speechConfig.value = {
        key: result.data[0],
        region: result.data[1]
      }
    }
  })
})
</script>

<template>
  <a-card
    :size="'small'"
    class="container"
  >
    <App
      :conversation="conversation"
      :questions="questions"
      :models="models"
      :messages="messages"
      :documents="documents"
      :accept="accept"
      :speech-config="speechConfig"
      :find-conversations="findConversations"
      :rename-conversation="renameConversation"
      :remove-conversation="removeConversation"
      :clear-memory="clearMemory"
      :send="send"
      :upload-media="uploadMedia"
      :remove-media="removeMedia"
      :upload-document="uploadDocument"
      :remove-document="removeDocument"
      @new-conversation="newConversation"
      @goto="goto"
    />
  </a-card>
</template>

<style lang="less" scoped>
.container {
  > :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 152px - 1px);
  }
}
</style>
