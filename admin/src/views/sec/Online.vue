<script setup>
import { ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import OnlineApi from '@/api/sec/online.js'

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '登录账号',
    field: 'account',
    icon: 'IdcardOutlined',
    type: 'text'
  }, {
    title: '名称',
    field: 'name',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: '手机号码',
    field: 'mp',
    icon: 'MobileOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: '登录账号',
    dataIndex: 'account',
    sorter: true
  }, {
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '手机号码',
    dataIndex: 'mp'
  }, {
    title: '性别',
    dataIndex: 'isMale',
    type: 'select',
    config: {
      options: [{
        label: '男性',
        value: true
      }, {
        label: '女性',
        value: false
      }]
    }
  }],
  actions: [{
    title: '下线',
    icon: 'LogoutOutlined',
    callback (record) {
      OnlineApi.logout(record.id).then(() => {
        table.value.load()
      })
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    OnlineApi.search(count, index, sorters, _filters.account, _filters.name, _filters.mp, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
