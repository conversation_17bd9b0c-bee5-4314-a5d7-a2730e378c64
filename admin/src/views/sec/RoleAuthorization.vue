<script setup>
import { getCurrentInstance, onMounted, ref } from 'vue'
import StatefulButton from '@/components/StatefulButton.vue'
import PermissionApi from '@/api/sec/permission.js'
import RoleApi from '@/api/sec/role.js'

const { proxy } = getCurrentInstance()

const checkedKeys = ref([])
const expandedKeys = ref([])

const dataSource = ref([])

const save = () => {
  const _promise = RoleApi.savePermission(proxy.$route.query.id, checkedKeys.value)

  _promise.then(() => {
    authorized()
  })

  return _promise
}

const array2Tree = (array, parentId) => {
  const _tree = []

  array.forEach(i => {
    if (i.parentId === parentId) {
      const _node = {
        key: i.id,
        title: i.name,
        children: array2Tree(array, i.id)
      }

      _tree.push(_node)
    }
  })

  return _tree
}

const authorized = () => {
  RoleApi.findPermissions(proxy.$route.query.id, {
    toast: {
      success: false
    }
  }).then(result => {
    // 设置选中
    checkedKeys.value = result.data.map(i => i.id)
  })
}

const ready = ref(false)
const role = ref({
  name: null,
  description: null,
  creatorName: null,
  createTime: null
})
onMounted(() => {
  ready.value = true

  RoleApi.get(proxy.$route.query.id, {
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    role.value = result.data
  })

  PermissionApi.find({
    toast: {
      success: false
    }
  }).then(result => {
    dataSource.value = array2Tree(result.data, null)

    // 展开首层
    expandedKeys.value = dataSource.value.map(i => i.key)

    authorized()
  })
})
</script>

<script>
export default {
  beforeRouteEnter (to, from, next) {
    if (Object.prototype.hasOwnProperty.call(to, 'query') && Object.prototype.hasOwnProperty.call(to.query, 'id')) {
      next()
    } else {
      next({
        path: '/sec/role'
      })
    }
  }
}
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header
        :title="'角色授权'"
        :sub-title="role.name || '-'"
        @back="() => $router.go(-1)"
      >
        <template #extra>
          <StatefulButton
            :icon="'SaveOutlined'"
            :type="'primary'"
            :on-click="save"
          >
            保存
          </StatefulButton>
        </template>
        <a-divider />
        <a-descriptions
          :column="{ xs: 1, md: 3, xl: 6 }"
          :size="'small'"
        >
          <a-descriptions-item :label="'角色描述'">
            {{ role.description || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建人员'">
            {{ role.creatorName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item :label="'创建时间'">
            {{ role.createTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-page-header>
    </teleport>
  </template>

  <div class="layout-content-panel">
    <a-tree
      v-model:checked-keys="checkedKeys"
      v-model:expanded-keys="expandedKeys"
      :checkable="true"
      :show-icon="false"
      :show-line="true"
      :tree-data="dataSource"
    />
  </div>
</template>
