<script setup>
import { ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import LogApi from '@/api/sys/log.js'

const _realmOptions = [{
  label: '账号密码',
  value: 'PASSWORD'
}, {
  label: '短信验证码',
  value: 'SMS'
}, {
  label: '手机号码',
  value: 'MP'
}, {
  label: '微信企业号',
  value: 'WXCP'
}, {
  label: '粤政易',
  value: 'WXYZY'
}, {
  label: 'DEBUG',
  value: 'CONFIG'
}]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '方法',
    field: 'method',
    icon: 'CodeOutlined',
    type: 'text'
  }, {
    title: '认证方式',
    field: 'realmType',
    type: 'select',
    config: {
      options: _realmOptions
    }
  }, {
    title: '用户账号',
    field: 'account',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: 'IP',
    field: 'ip',
    icon: 'icon-IP',
    type: 'text'
  }, {
    title: '时间',
    field: 'range',
    type: 'range',
    config: {
      placeholder: ['开始日期', '结束日期']
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: '事务编号',
    dataIndex: 'transactionId'
  }, {
    title: '方法',
    dataIndex: 'method',
    sorter: true
  }, {
    title: '用户',
    dataIndex: 'actorName'
  }, {
    title: 'IP',
    dataIndex: 'ip'
  }, {
    title: '认证方式',
    dataIndex: 'realmType',
    type: 'select',
    config: {
      options: _realmOptions
    }
  }, {
    title: '开始时间',
    dataIndex: 'startTime',
    sorter: true
  }, {
    title: '结束时间',
    dataIndex: 'endTime',
    sorter: true
  }, {
    title: '历时（毫秒）',
    dataIndex: 'period'
  }, {
    title: '异常信息',
    dataIndex: 'exception'
  }],
  actions: [{
    title: '浏览',
    icon: 'SearchOutlined',
    callback (record) {
      form.value.request = record.request
      form.value.response = typeof record.exception === 'string' ? (record.exception + '\n') : ''
      form.value.response += record.stackTrace || ''

      modal.value.show()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  let _beginTime = null
  let _endTime = null
  if (Array.isArray(_filters.range) && _filters.range.length > 0) {
    _beginTime = _filters.range[0].format('YYYY-MM-DD')
    _endTime = _filters.range.length > 1 ? _filters.range[1].format('YYYY-MM-DD') : null
  }

  return new Promise(resolve => {
    LogApi.error(count, index, sorters, _filters.method, _filters.realmType, _filters.account, _filters.ip, _beginTime, _endTime, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const form = ref({
  activeKeys: [1, 2],
  request: null,
  response: null
})

const modal = ref()

const buttons = [{
  icon: 'CloseOutlined',
  title: '关闭',
  type: 'primary',
  onClick () {
    modal.value.close()

    return Promise.resolve()
  }
}]

const onClose = () => {
  form.value.activeKeys = [1, 2]
  form.value.request = null
  form.value.response = null
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>

  <StatefulModal
    ref="modal"
    :buttons="buttons"
    @close="onClose"
  >
    <a-collapse v-model:active-key="form.activeKeys">
      <a-collapse-panel
        :key="1"
        :header="'请求体'"
      >
        <p>{{ form.request }}</p>
      </a-collapse-panel>
      <a-collapse-panel
        :key="2"
        :header="'响应体'"
      >
        <p>{{ form.response }}</p>
      </a-collapse-panel>
    </a-collapse>
  </StatefulModal>
</template>
