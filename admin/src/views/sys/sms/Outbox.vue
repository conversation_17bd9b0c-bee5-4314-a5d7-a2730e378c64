<script setup>
import { ref } from 'vue'
import DataTable from '@/components/DataTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import SmsApi from '@/api/sys/sms.js'

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '手机号码',
    field: 'mp',
    icon: 'MobileOutlined',
    type: 'text'
  }, {
    title: '参数/内容',
    field: 'param',
    icon: 'FileTextOutlined',
    type: 'text'
  }, {
    title: '状态',
    field: 'status',
    type: 'select',
    config: {
      options: [{
        label: '等待',
        value: 'TODO'
      }, {
        label: '完成',
        value: 'DONE'
      }, {
        label: '失败',
        value: 'FAILED'
      }, {
        label: '取消',
        value: 'CANCELED'
      }]
    }
  }, {
    title: '时间',
    field: 'range',
    type: 'range',
    config: {
      placeholder: ['开始日期', '结束日期'],
      showTime: true
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  columns: [{
    title: 'id',
    dataIndex: 'id'
  }, {
    title: '手机号码',
    dataIndex: 'mps',
    sorter: true
  }, {
    title: '模板',
    dataIndex: 'templateId',
    sorter: true
  }, {
    title: '参数/内容',
    dataIndex: 'params'
  }, {
    title: '状态',
    dataIndex: 'status',
    type: 'select',
    config: {
      options: [{
        label: '等待',
        value: 'TODO'
      }, {
        label: '完成',
        value: 'DONE'
      }, {
        label: '失败',
        value: 'FAILED'
      }, {
        label: '取消',
        value: 'CANCELED'
      }]
    }
  }, {
    title: '发送人员',
    dataIndex: 'creatorName'
  }, {
    title: '发送时间',
    dataIndex: 'createTime',
    sorter: true
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  let _beginTime = null
  let _endTime = null
  if (Array.isArray(_filters.range) && _filters.range.length > 0) {
    _beginTime = _filters.range[0].format('YYYY-MM-DD HH:mm:ss')
    _endTime = _filters.range.length > 1 ? _filters.range[1].format('YYYY-MM-DD HH:mm:ss') : null
  }

  return new Promise(resolve => {
    SmsApi.search(count, index, sorters, _filters.mp, _filters.param, _filters.status, _beginTime, _endTime, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      result.data.records.forEach(value => {
        if (Array.isArray(value.mps)) {
          value.mps = value.mps.join(', ')
        }
      })

      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <DataTable
      ref="table"
      :columns="tableOptions.columns"
      :load="load"
    />
  </div>
</template>
