<script setup>
import { computed, nextTick, ref } from 'vue'
import { Grid } from 'ant-design-vue'
import ProfileEditor from '@/components/ProfileEditor.vue'
import StatefulButton from '@/components/StatefulButton.vue'
import ProfileApi from '@/api/sys/profile.js'

const catalog = ref({
  keys: [1],
  items: [{
    key: 1,
    label: '个人设置'
  }, {
    key: 2,
    label: '安全设置'
  }],
  mode: 'vertical'
})

// 小屏垂直布局，大屏水平布局
const breakpoint = Grid.useBreakpoint()
nextTick(() => {
  catalog.value.mode = breakpoint.value.xs ? 'horizontal' : 'vertical'
})

const title = computed({
  get () {
    switch (catalog.value.keys[0]) {
      case 1:
        return '个人设置'
      case 2:
        return '修改密码'
      default:
        return ''
    }
  }
})

const password = ref({
  origin: null,
  input: null,
  confirm: null
})

const confirmRules = [{
  required: true,
  message: '请再次输入密码'
}, {
  validator: async (rule, value) => {
    if (value !== '' && value !== password.value.input) {
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject('两次输入的密码不一致')
    } else {
      return Promise.resolve()
    }
  }
}]

// 修改密码
const reset = ref()
const changePassword = () => {
  return reset.value.validate()
    .then(() => new Promise(resolve => {
      ProfileApi.changePassword(password.value.origin, password.value.input).finally(() => {
        resolve()
      })
    }))
    .catch(() => Promise.resolve())
}

const getProfile = () => {
  return ProfileApi.get({
    toast: {
      success: false
    }
  })
}

const saveProfile = model => {
  return ProfileApi.save(model)
}
</script>

<template>
  <a-card>
    <div class="account-settings">
      <!-- 左侧菜单 -->
      <div class="account-settings-menu">
        <a-menu
          v-model:selected-keys="catalog.keys"
          :items="catalog.items"
          :mode="catalog.mode"
        />
      </div>

      <!-- 右侧内容 -->
      <div class="account-settings-info">
        <template v-if="breakpoint.xs === false">
          <div class="account-settings-info-title">
            {{ title }}
          </div>
        </template>

        <template v-if="title === '个人设置'">
          <ProfileEditor
            :get="getProfile"
            :save="saveProfile"
          />
        </template>

        <template v-else-if="title === '修改密码'">
          <a-row>
            <a-col
              :xs="24"
              :xl="12"
            >
              <a-form
                ref="reset"
                :model="password"
                :label-col="{ style: { width: '150px' } }"
              >
                <a-form-item
                  :label="'原密码'"
                  :name="'origin'"
                  :rules="[{
                    required: true,
                    message: '请输入原密码'
                  }]"
                >
                  <a-input-password v-model:value="password.origin" />
                </a-form-item>

                <a-form-item
                  :label="'新密码'"
                  :name="'input'"
                  :rules="[{
                    required: true,
                    message: '请输入新密码'
                  }, {
                    pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/,
                    message: '请输入强密码（大小写字母+数字+特殊符号组成，且长度至少为8位）'
                  }]"
                >
                  <a-input-password v-model:value="password.input" />
                </a-form-item>

                <a-form-item
                  :label="'再次输入新密码'"
                  :name="'confirm'"
                  :rules="confirmRules"
                >
                  <a-input-password v-model:value="password.confirm" />
                </a-form-item>

                <a-form-item :wrapper-col="{ style: { left: '150px' } }">
                  <StatefulButton
                    :icon="'SaveOutlined'"
                    :type="'primary'"
                    :on-click="changePassword"
                  >
                    保存
                  </StatefulButton>
                </a-form-item>
              </a-form>
            </a-col>
          </a-row>
        </template>
      </div>
    </div>
  </a-card>
</template>

<style lang="less" scoped>
@import '@/less/default';

.ant-card-body {
  height: 100%;
}

.account-settings {
  display: flex;
  width: 100%;
  height: 100%;

  .account-settings-menu {
    width: 224px;
    border-right: 1px solid rgba(5, 5, 5, 0.06);
  }

  .account-settings-info {
    flex: 1 1;
    padding: @padding-xs 40px;

    .account-settings-info-title {
      margin-bottom: @margin-lg;
      padding-bottom: @margin-sm;
      border-bottom: 1px solid rgba(5, 5, 5, 0.06);
      font-size: 20px;
    }

    /* .account-settings-info-main {
      min-width: 224px;
      max-width: 448px;
    } */
  }
}

@media screen and (max-width: @screen-md-min) {
  .account-settings {
    flex-direction: column;

    .account-settings-info {
      padding: @padding-xs 0;
    }

    .account-settings-menu {
      width: 100%;
      border-right: none;
    }
  }
}
</style>
