<script setup>
import { inject, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import jquery from 'jquery'
import StatefulButton from '@/components/StatefulButton.vue'
import StatisticApi from '@/api/sys/statistic.js'

const gutter = [8, 8]

const span = {
  xs: 24,
  md: 8
}

// 绘制折线图
const drawLineChart = (chart, x, y, options, percent) => {
  const _option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: x
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: percent ? '{value}%' : null
      }
    },
    series: [{
      type: 'line',
      data: y,
      smooth: true,
      label: {
        show: true,
        position: 'center',
        formatter: percent ? '{c}%' : null
      },
      areaStyle: {}
    }]
  }

  if (jquery.isPlainObject(options)) {
    jquery.extend(true, _option, options)
  }

  chart.setOption(_option)
}

// 绘制饼图
const drawPieChart = (chart, y) => {
  const _option = {
    series: [{
      type: 'pie',
      data: y,
      radius: ['40%', '70%'],
      label: {
        show: true,
        position: 'inside',
        formatter: '{b}（{c}%）',
        color: '#000'
      },
      itemStyle: {
        borderWidth: 2,
        borderRadius: 10,
        borderColor: '#80FFA5',
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgb(128, 255, 165)'
        }, {
          offset: 1,
          color: 'rgb(1, 191, 236)'
        }])
      }
    }]
  }

  chart.setOption(_option)
}

// 重绘图形
const redrawChart = index => {
  let _promise
  switch (index) {
    case 'dau':
      _promise = dauChart.render()
      _promise.then(data => {
        drawLineChart(dauChart.component, data.x, data.y, {
          series: [{
            name: '用户数',
            itemStyle: {
              normal: {
                color: '#37A2FF'
              }
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgb(55, 162, 255)'
              }, {
                offset: 1,
                color: 'rgb(116, 21, 219)'
              }])
            }
          }]
        })
      })

      break
    case 'dar':
      _promise = darChart.render()
      _promise.then(data => {
        drawLineChart(darChart.component, data.x, data.y, {
          series: [{
            name: '活跃率',
            itemStyle: {
              normal: {
                color: '#37A2FF'
              }
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgb(55, 162, 255)'
              }, {
                offset: 1,
                color: 'rgb(116, 21, 219)'
              }])
            }
          }]
        }, true)
      })

      break
    case 'urr':
      _promise = urrChart.render()
      _promise.then(num => {
        drawPieChart(urrChart.component, [{
          name: '留存',
          value: toPercentage(num)
        }, {
          name: '流失',
          value: toPercentage(1 - num)
        }])
      })

      break
    case 'mru':
      _promise = getMRU()
      _promise.then(data => {
        mru.value.dataSource = data
      })

      break
    case 'trend':
      _promise = trendChart.render()
      _promise.then(data => {
        drawLineChart(trendChart.component, data.x, data.y, {
          series: [{
            name: '负载',
            itemStyle: {
              normal: {
                color: '#FFBF00'
              }
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgb(255, 191, 0)'
              }, {
                offset: 1,
                color: 'rgb(224, 62, 76)'
              }])
            }
          }]
        }, false)
      })

      break
    default:
      _promise = Promise.resolve()
  }

  return _promise
}

// 百分比化
const toPercentage = (num) => {
  return Math.round(num * 10000) / 100.00
}

// 日活跃用户数
const dau = ref(null)
const dauChart = {
  component: null,
  loading: false,
  render () {
    const that = this

    that.component.showLoading()
    that.loading = true

    return new Promise(resolve => {
      const _end = Date.today()

      StatisticApi.dau(_end.addDays(-6), _end, null, null, {
        showLoading: false,
        toast: {
          success: false
        }
      })
        .then(result => {
          const _x = []
          const _y = []

          result.data.forEach(i => {
            _x.push(i.one.split(' ')[0])
            _y.push(i.two)
          })

          resolve({
            x: _x,
            y: _y
          })
        })
        .catch(() => resolve({
          x: [],
          y: []
        }))
        .finally(() => {
          that.loading = false
          that.component.hideLoading()
        })
    })
  }
}

// 日活跃率
const dar = ref(null)
const darChart = {
  component: null,
  loading: false,
  render () {
    const that = this

    that.component.showLoading()
    that.loading = true

    return new Promise(resolve => {
      const _end = Date.today()

      const _x = []
      const _y = []

      StatisticApi.dar(_end.addDays(-6), _end, null, {
        showLoading: false,
        toast: {
          success: false
        }
      })
        .then(result => {
          result.data.forEach(i => {
            _x.push(i.one.split(' ')[0])
            _y.push(toPercentage(i.two))
          })
        })
        .finally(() => {
          that.loading = false
          that.component.hideLoading()

          resolve({
            x: _x,
            y: _y
          })
        })
    })
  }
}

// 月留存率
const urr = ref(null)
const urrChart = {
  component: null,
  loading: false,
  render () {
    const that = this

    that.component.showLoading()
    that.loading = true

    return new Promise(resolve => {
      StatisticApi.urr(2, {
        showLoading: false,
        toast: {
          success: false
        }
      })
        .then(result => {
          resolve(result.data)
        })
        .catch(() => {
          resolve(0)
        })
        .finally(() => {
          that.loading = false
          that.component.hideLoading()
        })
    })
  }
}

// 7日活跃分时图
const trend = ref(null)
const trendChart = {
  component: null,
  loading: false,
  render () {
    const that = this

    that.component.showLoading()
    that.loading = true

    return new Promise(resolve => {
      const _end = Date.today()

      const _x = []
      const _y = []

      for (let i = 0; i < 24; i++) {
        _x.push(i.toString().padStart(2, '0') + ':00')
        _y.push(0)
        _x.push(i.toString().padStart(2, '0') + ':30')
        _y.push(0)
      }

      StatisticApi.trend(_end.addDays(-6), _end, {
        showLoading: false,
        toast: {
          success: false
        }
      })
        .then(result => {
          result.data.forEach(i => {
            const _i = i.one / 60 * 2
            const _j = i.one % 30

            _y[_i + _j] = i.two
          })
        })
        .finally(() => {
          that.loading = false
          that.component.hideLoading()

          resolve({
            x: _x,
            y: _y
          })
        })
    })
  }
}

// 月度访问TOP10
const mru = ref({
  columns: [{
    title: '方法',
    dataIndex: 'method',
    align: 'center',
    ellipsis: true
  }, {
    title: '访问人数',
    dataIndex: 'users',
    align: 'center'
  }, {
    title: '访问人次',
    dataIndex: 'visits',
    align: 'center'
  }],
  dataSource: [],
  showLoading: false
})
const getMRU = () => {
  mru.value.loading = true

  return new Promise(resolve => {
    StatisticApi.mru(10, new Date(), 2, {
      showLoading: false,
      toast: {
        success: false
      }
    })
      .then(result => {
        resolve(result.data)
      })
      .catch(() => {
        resolve([])
      })
      .finally(() => {
        mru.value.loading = false
      })
  })
}

// 折叠菜单时重置各图表
const sidebar = inject('sidebar')
watch(
  () => sidebar.value.open,
  () => {
    // 延迟重置
    setTimeout(() => {
      [dauChart, darChart, urrChart, trendChart]
        .filter(i => i.component !== null).forEach(i => {
          i.component.resize()
        })
    }, 200)
  }
)

onMounted(() => {
  // 日活跃用户
  dauChart.component = echarts.init(dau.value)
  redrawChart('dau')

  // 日活跃率
  darChart.component = echarts.init(dar.value)
  redrawChart('dar')

  // 月留存率
  urrChart.component = echarts.init(urr.value)
  redrawChart('urr')

  // 7日活跃分时图
  trendChart.component = echarts.init(trend.value)
  redrawChart('trend')

  redrawChart('mru')
})
</script>

<template>
  <div class="statistic-container">
    <a-row
      :align="'middle'"
      :gutter="gutter"
    >
      <a-col
        :md="span.md"
        :xs="span.xs"
      >
        <a-card
          :size="'small'"
          :title="'日活跃用户'"
        >
          <template #extra>
            <StatefulButton
              :disable-style="'disabled'"
              :icon="'ReloadOutlined'"
              :on-click="() => redrawChart('dau')"
              :type="'link'"
            />
          </template>
          <div
            ref="dau"
            class="chart-container"
          />
        </a-card>
      </a-col>

      <a-col
        :md="span.md"
        :xs="span.xs"
      >
        <a-card
          :size="'small'"
          :title="'日活跃率'"
        >
          <template #extra>
            <StatefulButton
              :disable-style="'disabled'"
              :icon="'ReloadOutlined'"
              :on-click="() => redrawChart('dar')"
              :type="'link'"
            />
          </template>
          <div
            ref="dar"
            class="chart-container"
          />
        </a-card>
      </a-col>

      <a-col
        :md="span.md"
        :xs="span.xs"
      >
        <a-card
          :size="'small'"
          :title="'月留存率'"
        >
          <template #extra>
            <StatefulButton
              :disable-style="'disabled'"
              :icon="'ReloadOutlined'"
              :on-click="() => redrawChart('urr')"
              :type="'link'"
            />
          </template>
          <div
            ref="urr"
            class="chart-container"
          />
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :align="'middle'"
      :gutter="gutter"
    >
      <a-col :span="24">
        <a-card
          :size="'small'"
          :title="'7日活跃分时图'"
        >
          <template #extra>
            <StatefulButton
              :disable-style="'disabled'"
              :icon="'ReloadOutlined'"
              :on-click="() => redrawChart('trend')"
              :type="'link'"
            />
          </template>
          <div
            ref="trend"
            class="chart-container"
          />
        </a-card>
      </a-col>
    </a-row>
    <a-row
      :align="'middle'"
      :gutter="gutter"
    >
      <a-col :span="24">
        <a-card
          :size="'small'"
          :title="'月度访问TOP10'"
          class="mru"
        >
          <template #extra>
            <StatefulButton
              :disable-style="'disabled'"
              :icon="'ReloadOutlined'"
              :on-click="() => redrawChart('mru')"
              :type="'link'"
            />
          </template>
          <a-table
            :columns="mru.columns"
            :data-source="mru.dataSource"
            :loading="mru.loading"
            :pagination="false"
            :size="'small'"
          >
            <template #emptyText>
              <a-empty description="暂无数据" />
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.statistic-container {
  .ant-row {
    margin-bottom: @margin-xs !important;

    .ant-card-body {
      padding: 0 @padding-lg;
    }

    .chart-container {
      height: 250px;
    }

    .mru {
      .ant-card-body {
        padding: 0;
      }
    }
  }
}
</style>
