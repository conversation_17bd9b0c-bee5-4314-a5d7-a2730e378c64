<script setup>
import { computed, createVNode, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import EdiTable from '@/components/EdiTable.vue'
import OrganizationPicker from '@/components/OrganizationPicker.vue'
import SearchBar from '@/components/SearchBar.vue'
import StatefulButton from '@/components/StatefulButton.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import FeedbackUtil from '@/utils/feedback.js'
import DepartmentApi from '@/api/sys/department.js'
import UserApi from '@/api/sys/user.js'

const gutter = [8, 8]

const _statusOptions = [{
  label: '生效',
  value: true
}, {
  label: '失效',
  value: false
}]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '登录账号',
    field: 'account',
    icon: 'IdcardOutlined',
    type: 'text'
  }, {
    title: '名称',
    field: 'username',
    icon: 'UserOutlined',
    type: 'text'
  }, {
    title: '手机号码',
    field: 'mp',
    icon: 'MobileOutlined',
    type: 'text'
  }, {
    title: '是否生效',
    field: 'isEnabled',
    type: 'select',
    config: {
      options: _statusOptions
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const organization = ref({
  current: {
    id: null,
    code: null,
    name: null,
    fullName: null
  },
  subordinates: []
})

const loadOrganization = rootId => {
  DepartmentApi.organization(rootId, {
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    organization.value.current = result.data.department
    organization.value.subordinates = result.data.subordinates

    table.value.load()
  })
}

const removeDepartment = department => {
  const _promise = DepartmentApi.remove(department.id, {
    showLoading: false
  })

  _promise.then(() => {
    loadOrganization(organization.value.current.id)
  })

  return _promise
}

// 导航栏
const navIds = computed(() => {
  return organization.value.current.code === null ? [] : organization.value.current.code.split('.')
})
const navNames = computed(() => {
  return organization.value.current.fullName === null ? [] : organization.value.current.fullName.split('/')
})

const departmentEditor = ref()
const departmentEditorConfig = ref({
  title: null,
  buttons: [{
    title: '保存',
    icon: 'SaveOutlined',
    type: 'primary',
    onClick () {
      return departmentForm.value.validate()
        .then(() => {
          return new Promise(resolve => {
            DepartmentApi.save(departmentModel.value, {
              showLoading: false
            })
              .then(() => {
                // 关闭窗口
                departmentEditor.value.close()

                // 刷新数据
                loadOrganization(departmentModel.value.superiorId)
              })
              .finally(() => {
                resolve()
              })
          })
        })
        .catch(() => {
          return Promise.resolve()
        })
    }
  }],
  close: () => {
    departmentModel.value = {
      id: null,
      superiorId: null,
      name: null
    }
  }
})
const departmentForm = ref()
const departmentModel = ref({
  id: null,
  superiorId: null,
  name: null
})

const editDepartment = (current, superior) => {
  if (current === null) {
    departmentEditorConfig.value.title = `在[${superior.id === null ? '根部门' : superior.name}]下新建子部门`
    departmentModel.value = {
      id: null,
      superiorId: superior.id,
      name: null
    }
  } else {
    departmentEditorConfig.value.title = `编辑[${current.name}]`
    departmentModel.value = {
      id: current.id,
      superiorId: current.superiorId,
      name: current.name
    }
  }

  departmentEditor.value.show()
}

const organizationPicker = ref({
  id: null
})
const pickDepartment = department => {
  organizationPicker.value.id = department.id

  organizationPicker.value.show()
}

const moveDepartment = departments => {
  const _superiorId = departments.length > 0 ? departments[0].id : null

  DepartmentApi.move(organizationPicker.value.id, _superiorId).then(() => {
    loadOrganization(_superiorId)
  })
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/sys/user',
    idField: 'id'
  },
  columns: [{
    title: '登录账号',
    dataIndex: 'account',
    sorter: true
  }, {
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '部门',
    dataIndex: 'deptFullName'
  }, {
    title: '手机号码',
    dataIndex: 'mp',
    sorter: true
  }, {
    title: '性别',
    dataIndex: 'isMale',
    type: 'select',
    config: {
      options: [{
        label: '男性',
        value: true
      }, {
        label: '女性',
        value: false
      }]
    }
  }, {
    title: '是否生效',
    dataIndex: 'isEnabled',
    type: 'select',
    config: {
      options: [{
        label: '生效',
        value: true
      }, {
        label: '失效',
        value: false
      }]
    }
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }],
  actions: [{
    title: '禁用',
    icon: 'StopOutlined',
    danger: true,
    callback (record) {
      return new Promise(resolve => {
        FeedbackUtil.modal('即将禁用该账号，是否确认？', 'confirm', {
          icon: createVNode(ExclamationCircleOutlined),
          onOk () {
            UserApi.enable(record.id, false, {
              showLoading: false
            })
              .then(() => {
                table.value.load()
              })
              .finally(() => {
                resolve()
              })
          },
          onCancel () {
            resolve()
          }
        })
      })
    }
  }, {
    title: '启用',
    icon: 'UndoOutlined',
    callback (record) {
      return new Promise(resolve => {
        FeedbackUtil.modal('即将启用该账号，是否确认？', 'confirm', {
          icon: createVNode(ExclamationCircleOutlined),
          onOk () {
            UserApi.enable(record.id, true, {
              showLoading: false
            })
              .then(() => {
                table.value.load()
              }).finally(() => {
                resolve()
              })
          },
          onCancel () {
            resolve()
          }
        })
      })
    }
  }, {
    title: '重置密码',
    icon: 'UnlockOutlined',
    callback (record) {
      passwordModel.value.userId = record.id

      passwordEditor.value.show()
    }
  }]
}

const loadUsers = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    DepartmentApi.members(count, index, sorters, organization.value.current.id, _filters.account, _filters.username, _filters.mp, _filters.isEnabled, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      const _records = result.data.records || result.data.items
      _records.forEach(i => {
        i._actions_ = i.isEnabled ? [0, 1, 3] : [2]
      })

      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const passwordEditor = ref()
const passwordEditorConfig = ref({
  open: false,
  buttons: [{
    title: '保存',
    icon: 'CheckOutlined',
    type: 'primary',
    onClick () {
      return passwordForm.value.validate()
        .then(() => {
          return new Promise(resolve => {
            UserApi.resetPassword(passwordModel.value.userId, passwordModel.value.password)
              .then(() => {
                // 关闭窗口
                passwordEditor.value.close()
              })
              .finally(() => {
                resolve()
              })
          })
        })
        .catch(() => {
          return Promise.resolve()
        })
    }
  }],
  close: () => {
    passwordModel.value = {
      userId: null,
      password: null
    }
  }
})
const passwordForm = ref()
const passwordModel = ref({
  userId: null,
  password: null
})

onMounted(() => {
  loadOrganization(null)
})
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <a-row :gutter="gutter">
      <!-- 部门 -->
      <a-col
        :md="8"
        :xl="6"
        :xs="24"
      >
        <a-card
          :size="'small'"
          style="height: 100%"
        >
          <!-- 导航栏 -->
          <a-breadcrumb style="padding: 8px">
            <a-breadcrumb-item>
              <a @click="loadOrganization(null)">
                <home-outlined />
              </a>
            </a-breadcrumb-item>
            <template v-if="navIds.length > 0">
              <template
                v-for="(i, index) in navIds"
                :key="i"
              >
                <a-breadcrumb-item>
                  <a @click="loadOrganization(i)">
                    {{ navNames[index] }}
                  </a>
                </a-breadcrumb-item>
              </template>
            </template>
          </a-breadcrumb>

          <!-- 列表 -->
          <a-list
            :data-source="organization.subordinates"
            :item-layout="'horizontal'"
          >
            <template #renderItem="{ item }">
              <div style="padding: 8px; border-top: 1px solid #f0f0f0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between;">
                <!-- 标签 -->
                <a
                  style="text-overflow: ellipsis; overflow: hidden"
                  @click="loadOrganization(item.id)"
                >
                  <bank-outlined />
                  {{ item.name }}
                </a>

                <!-- 动作 -->
                <div style="border-left: 1px solid #f0f0f0;">
                  <a-button
                    :size="'small'"
                    :title="'编辑'"
                    :type="'link'"
                    @click="editDepartment(item, organization.current)"
                  >
                    <template #icon>
                      <edit-outlined />
                    </template>
                  </a-button>
                  <StatefulButton
                    :danger="true"
                    :icon="'DeleteOutlined'"
                    :size="'small'"
                    :title="'删除'"
                    :type="'link'"
                    :on-click="() => removeDepartment(item)"
                  />
                  <a-button
                    :size="'small'"
                    :title="'新增子部门'"
                    :type="'link'"
                    @click="editDepartment(null, item);"
                  >
                    <template #icon>
                      <plus-outlined />
                    </template>
                  </a-button>
                  <a-button
                    :size="'small'"
                    :title="'移动'"
                    :type="'link'"
                    @click="pickDepartment(item)"
                  >
                    <template #icon>
                      <drag-outlined />
                    </template>
                  </a-button>
                </div>
              </div>
            </template>

            <!-- 新建 -->
            <template #loadMore>
              <div style="padding: 8px;text-align: center;">
                <a-button
                  :type="'link'"
                  @click="editDepartment(null, organization.current)"
                >
                  <template #icon>
                    <plus-outlined />
                  </template>
                  新建子部门
                </a-button>
              </div>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 用户 -->
      <a-col
        :md="16"
        :xl="18"
        :xs="24"
      >
        <a-card :size="'small'">
          <EdiTable
            ref="table"
            :mapper="tableOptions.mapper"
            :actions="tableOptions.actions"
            :columns="tableOptions.columns"
            :addable="true"
            :load="loadUsers"
            :load-after-initialized="false"
            :support-selected="true"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>

  <!-- 部门编辑器 -->
  <StatefulModal
    ref="departmentEditor"
    :buttons="departmentEditorConfig.buttons"
    :title="departmentEditorConfig.title"
    @close="departmentEditorConfig.close"
  >
    <a-form
      ref="departmentForm"
      v-model:model="departmentModel"
    >
      <a-form-item
        :rules="[{
          required: true,
          message: '请输入部门名称'
        }]"
        name="name"
      >
        <a-input
          v-model:value="departmentModel.name"
          placeholder="请输入部门名称"
        >
          <template #prefix>
            <apartment-outlined />
          </template>
        </a-input>
      </a-form-item>
    </a-form>
  </StatefulModal>

  <!-- 移动部门 -->
  <OrganizationPicker
    :id="organizationPicker.id"
    ref="organizationPicker"
    :max="1"
    :title="'移动部门'"
    :type="'department'"
    @picked="moveDepartment"
  />

  <!-- 重置密码 -->
  <StatefulModal
    ref="passwordEditor"
    :buttons="passwordEditorConfig.buttons"
    :title="'重置密码'"
    @close="passwordEditorConfig.close"
  >
    <a-form
      ref="passwordForm"
      v-model:model="passwordModel"
    >
      <a-form-item
        :rules="[{
          required: true,
          message: '请输入密码'
        }, {
          pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/,
          message: '请输入强密码（大小写字母+数字+特殊符号组成，且长度至少为8位）'
        }]"
        name="password"
      >
        <a-input-password
          v-model:value="passwordModel.password"
          :placeholder="'请输入密码'"
        >
          <template #prefix>
            <lock-outlined />
          </template>
        </a-input-password>
      </a-form-item>
    </a-form>
  </StatefulModal>
</template>
