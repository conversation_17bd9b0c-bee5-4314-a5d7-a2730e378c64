import axios from 'axios'
import jquery from 'jquery'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import Store from '@/store/index.js'
import FeedbackUtil from '@/utils/feedback.js'

const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL

const onError = (result, options) => {
  switch ((result.code + '').substr(4)) {
    // 身份认证失效
    case '001_B_020':
      // 注销本地登录状态
      Store.commit('logout')

      if (options.onUnauthenticated.redirect) {
        FeedbackUtil.confirmToRedirectToLogin('您的身份验证已失效，是否重新登录？')
      } else if (options.toast.error) {
        FeedbackUtil.message(result.message == null || result.message === '' ? (`请求失败，错误代码为[${result.code}]`) : result.message, 'error'
        )
      }

      break
      // 其它
    default:
      if (options.toast.error) {
        FeedbackUtil.message(result.message == null || result.message === '' ? (`请求失败，错误代码为[${result.code}]`) : result.message, 'error')
      }

      break
  }
}

class HttpRequest {
  constructor (options) {
    const _options = {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      baseURL: API_BASE_URL,
      withCredentials: true
    }
    jquery.extend(true, _options, options)

    this.axios = axios.create(_options)
  }

  ajax (url, data, options) {
    const that = this

    const _options = {
      onUnauthenticated: {
        redirect: true
      },
      showLoading: true, // 请求时显示加载提示
      toast: {
        success: '请求成功', // 成功时显示成功信息
        error: true // 返回错误时显示错误信息
      }
    }
    jquery.extend(true, _options, options)

    // 要求显示加载
    if (_options.showLoading) {
      that.axios.interceptors.request.use(config => {
        FeedbackUtil.showLoading()

        return config
      })

      that.axios.interceptors.response.use(value => {
        FeedbackUtil.hideLoading()

        return value
      }, error => {
        FeedbackUtil.hideLoading()

        return error.response
          ? error.response
          : {
              data: {
                code: error.name,
                message: error.message
              }
            }
      })
    }

    return new Promise((resolve, reject) => {
      that.axios.post(url, data)
        .then(response => {
          let _promise
          switch (response.request.responseType) {
            case 'arraybuffer':
              _promise = new Promise(resolve => {
                try {
                  const _decoder = new TextDecoder('utf-8')
                  resolve(JSON.parse(_decoder.decode(new Uint8Array(response.data))))
                } catch (e) {
                  resolve({
                    code: 'OK',
                    data: {
                      name: decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1]),
                      content: response.data
                    }
                  })
                }
              })
              break
            case 'blob':
              _promise = new Promise(resolve => {
                const _reader = new FileReader()
                _reader.readAsText(response.data)
                _reader.onload = function () {
                  try {
                    resolve(JSON.parse(this.result))
                  } catch (e) {
                    resolve({
                      code: 'OK',
                      data: {
                        name: decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1]),
                        content: response.data
                      }
                    })
                  }
                }
              })
              break
            default:
              _promise = new Promise(resolve => resolve(response.data))
              break
          }

          _promise.then(result => {
            if (result.code === 'OK') {
              if (typeof _options.toast.success === 'string') {
                FeedbackUtil.message(_options.toast.success, 'success')
              }

              resolve(result)
            } else {
              onError(result, _options)

              reject(result)
            }
          })
        })
        .catch(error => {
          let _result
          if (error.response == null) {
            _result = {
              code: error.message,
              message: error.message
            }
          } else if (error.response.data == null) {
            _result = {
              code: error.response.status,
              message: error.message
            }
          } else {
            _result = Object.prototype.hasOwnProperty.call(error.response.data, 'message')
              ? error.response.data
              : {
                  code: error.response.status,
                  message: error.response.statusText
                }
          }

          onError(_result, _options)

          reject(_result)
        })
    })
  }

  eventSource (url, data, onopen, onmessage, onclose, onerror, options) {
    const _options = {
      onUnauthenticated: {
        redirect: true
      },
      showLoading: true, // 请求时显示加载提示
      toast: {
        success: '请求成功', // 成功时显示成功信息
        error: true // 返回错误时显示错误信息
      }
    }
    jquery.extend(true, _options, options)

    const handleError = error => {
      onError(error, _options)
      onerror && onerror(error)
    }

    return new Promise((resolve, reject) => {
      const controller = new AbortController()

      let _ok

      fetchEventSource(`${API_BASE_URL}${url}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(data),
        openWhenHidden: true,
        signal: controller.signal,
        async onopen (event) {
          // 是否成功响应
          _ok = event.ok
          if (_ok) {
            resolve(controller)
          }

          onopen && onopen(event)
        },
        async onmessage (event) {
          if (_ok) {
            onmessage && onmessage(event)
          } else {
            // 失败响应时，解析错误
            const _result = JSON.parse(event.data)
            handleError(_result)
          }
        },
        onclose (event) {
          onclose && onclose(event)
        },
        onerror (error) {
          handleError(error)
        }
      })
    })
  }
}

export default HttpRequest
