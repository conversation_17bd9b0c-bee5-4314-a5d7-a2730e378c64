const _pattern = {
  chineseName: /^[\u4e00-\u9fa5]{2,4}$/,
  identityNo: /(^\d{15}$)|(^\d{18}$)|(^\d{17}([dXx])$)/,
  mp: /^1(3[0-9]|4[01456879]|5[0-3,5-9]|6[2567]|7[0-8]|8[0-9]|9[0-3,5-9])\d{8}$/,
  email: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
}

export const pattern = {
  ..._pattern,
  isChineseName (str) {
    return pattern.chineseName.test(str)
  },
  isIdentityNo (str) {
    return pattern.identityNo.test(str)
  },
  isMp (str) {
    return pattern.mp.test(str)
  },
  isEmail (str) {
    return pattern.email.test(str)
  }
}
