import HttpRequest from '@/utils/http.js'

export default {
  installId (appId, options) {
    return new HttpRequest().ajax('/application/get/install-id', {
      appId
    }, options)
  },
  // 模糊搜索
  search (count, index, type, keyword, options) {
    return new HttpRequest().ajax('/application/search', {
      count,
      index,
      type,
      keyword
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/application/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/application/remove', {
      id
    }, options)
  }
}
