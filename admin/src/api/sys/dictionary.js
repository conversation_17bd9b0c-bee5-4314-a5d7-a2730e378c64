import HttpRequest from '@/utils/http.js'

export default {
  // 读取详情
  get (id, options) {
    return new HttpRequest().ajax('/sys/dictionary/get', {
      id
    }, options)
  },
  // 模糊搜索
  search (count, index, sortBy, groupId, name, isEnabled, options) {
    return new HttpRequest().ajax('/sys/dictionary/search', {
      count,
      index,
      sortBy,
      groupId,
      name,
      isEnabled
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/sys/dictionary/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/sys/dictionary/remove', {
      id
    }, options)
  }
}
