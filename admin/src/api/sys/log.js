import HttpRequest from '@/utils/http.js'

export default {
  info (count, index, sortBy, method, realmType, account, ip, code, startTime, endTime, options) {
    return new HttpRequest().ajax('/sys/log/search/info', {
      count,
      index,
      sortBy,
      method,
      realmType,
      account,
      ip,
      code,
      startTime,
      endTime
    }, options)
  },
  error (count, index, sortBy, method, realmType, account, ip, startTime, endTime, options) {
    return new HttpRequest().ajax('/sys/log/search/error', {
      count,
      index,
      sortBy,
      method,
      realmType,
      account,
      ip,
      startTime,
      endTime
    }, options)
  }
}
