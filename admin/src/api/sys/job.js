import HttpRequest from '@/utils/http.js'

export default {
  // 加载
  load (options) {
    return new HttpRequest().ajax('/sys/job/find', null, options)
  },
  // 修改计划
  updateCron (name, group, cron, options) {
    return new HttpRequest().ajax('/sys/job/update-cron', {
      name,
      group,
      cron
    }, options)
  },
  // 执行任务
  trigger (name, group, options) {
    return new HttpRequest().ajax('/sys/job/trigger', {
      name,
      group
    }, options)
  },
  // 暂停任务
  pause (name, group, options) {
    return new HttpRequest().ajax('/sys/job/pause', {
      name,
      group
    }, options)
  },
  // 恢复任务
  resume (name, group, options) {
    return new HttpRequest().ajax('/sys/job/resume', {
      name,
      group
    }, options)
  }
}
