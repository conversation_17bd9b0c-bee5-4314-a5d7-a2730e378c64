import HttpRequest from '@/utils/http.js'

export default {
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/sys/dept/save', record, options)
  },
  // 读取组织架构（下级部门+成员）
  organization (id, options) {
    return new HttpRequest().ajax('/sys/dept/organization', {
      id
    }, options)
  },
  // 搜索成员
  members (count, index, sortBy, id, account, username, mp, isEnabled, options) {
    return new HttpRequest().ajax('/sys/dept/user/search', {
      count,
      index,
      sortBy,
      id,
      account,
      username,
      mp,
      isEnabled
    }, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/sys/dept/remove', {
      id
    }, options)
  },
  // 移动
  move (id, superiorId, options) {
    return new HttpRequest().ajax('/sys/dept/move', {
      id,
      superiorId
    }, options)
  }
}
