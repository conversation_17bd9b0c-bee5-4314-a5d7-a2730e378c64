import Store from '@/store/index.js'
import HttpRequest from '@/utils/http.js'

export default {
  // 初始化应用数据
  init (options) {
    return new HttpRequest().ajax('/app/init', null, options)
  },
  // 读取应用
  getApp (options) {
    return new HttpRequest().ajax('/app/layout/header', {
      root: true
    }, options)
  },
  // 读取用户
  getUser (options) {
    return new Promise(resolve => {
      let _promise

      if (Store.state.assets.user === null) {
        _promise = new HttpRequest().ajax('/app/layout/user', null, options)

        _promise.then(result => {
          Store.commit('setUser', result.data)
        })
      } else {
        _promise = Promise.resolve()
      }

      _promise.then(() => {
        resolve({
          code: 'OK',
          data: Store.state.assets.user
        })
      })
    })
  },
  // 读取页面
  getPages (options) {
    if (Store.state.assets.pages === null) {
      return new HttpRequest().ajax('/app/layout/menu', null, options).then(result => {
        Store.commit('setPages', result.data)

        return Promise.resolve({
          code: 'OK',
          data: Store.state.assets.pages
        })
      })
    } else {
      return Promise.resolve({
        code: 'OK',
        data: Store.state.assets.pages
      })
    }
  }
}
