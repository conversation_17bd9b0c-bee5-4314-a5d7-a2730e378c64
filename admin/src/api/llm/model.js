import HttpRequest from '@/utils/http.js'

export default {
  // 读取详情
  get (id, options) {
    return new HttpRequest().ajax('/llm/model/get', {
      id
    }, options)
  },
  find (options) {
    return new HttpRequest().ajax('/llm/model/find', null, options)
  },
  // 模糊搜索
  search (count, index, sortBy, title, mode, options) {
    return new HttpRequest().ajax('/llm/model/search', {
      count,
      index,
      sortBy,
      title,
      mode
    }, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/llm/model/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/model/remove', {
      id
    }, options)
  }
}
