import HttpRequest from '@/utils/http.js'

export default {
  installId (appId, options) {
    return new HttpRequest().ajax('/llm/application/get/install-id', {
      appId
    }, options)
  },
  // 模糊搜索
  search (count, index, type, keyword, options) {
    return new HttpRequest().ajax('/llm/application/search', {
      count,
      index,
      type,
      keyword
    }, options)
  },
  me (options) {
    return new HttpRequest().ajax('/llm/application/me', null, options)
  },
  // 保存
  save (record, options) {
    return new HttpRequest().ajax('/llm/application/save', record, options)
  },
  // 删除
  remove (id, options) {
    return new HttpRequest().ajax('/llm/application/remove', {
      id
    }, options)
  }
}
