<script setup>
import { getCurrentInstance } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'

const { proxy } = getCurrentInstance()
</script>

<template>
  <!-- 全局配置 -->
  <a-config-provider
    :auto-insert-space-in-button="false"
    :locale="zhCN"
  >
    <!-- 全局加载 -->
    <a-spin :spinning="proxy.$store.getters.isLoading">
      <router-view />
    </a-spin>
  </a-config-provider>
</template>
