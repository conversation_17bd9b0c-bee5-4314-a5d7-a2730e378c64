<script setup>
import { ref, watch } from 'vue'
import UtilApi from '@/api/util.js'

const props = defineProps({
  id: {
    type: String,
    default: null
  },
  platform: {
    type: String,
    default: 'amap'
  },
  apiKey: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      amap: {
        key: null,
        code: null
      },
      qq: 'OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77'
    }
  },
  center: {
    type: Array,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  radius: {
    type: Number,
    default: null
  },
  total: {
    type: Number,
    default: null
  },
  zoom: {
    type: Number,
    default: null
  }
})

// 定义属性映射
const _mapper = {
  center: 'center',
  keyword: 'keyword',
  radius: 'radius',
  total: 'total',
  zoom: 'zoom'
}
switch (props.platform) {
  case 'amap':
    break
  case 'qq':
    _mapper.center = 'coord'
    _mapper.keyword = 'policy'
    break
}

const _params = []

// 设置公共参数
if (typeof props.center === 'string') {
  _params.push(`${_mapper.center}=${props.center}`)
}
if (typeof props.keyword === 'string') {
  _params.push(`${_mapper.keyword}=${props.keyword}`)
}
if (Number.isFinite(props.radius)) {
  _params.push(`${_mapper.radius}=${props.radius}`)
}
if (Number.isInteger(props.total)) {
  _params.push(`${_mapper.total}=${props.total}`)
}
if (Number.isInteger(props.zoom)) {
  _params.push(`${_mapper.zoom}=${props.zoom}`)
}

const src = ref('')
let _promise
switch (props.platform) {
  case 'amap':
    _promise = props.apiKey.amap.key && props.apiKey.amap.code
      ? Promise.resolve({
        data: {
          key: props.apiKey.amap.key,
          code: props.apiKey.amap.code
        }
      })
      : UtilApi.aMapJsKey({
        showLoading: false,
        toast: {
          success: false
        }
      })

    _promise.then(result => {
      _params.push(`key=${result.data.key}`)
      _params.push(`jscode=${result.data.code}`)
      src.value = `https://m.amap.com/picker/?${_params.join('&')}`
    })

    break
  case 'qq':
    _promise = props.apiKey.qq
      ? Promise.resolve({
        data: props.apiKey.qq
      })
      : UtilApi.qqMapJsKey({
        showLoading: false,
        toast: {
          success: false
        }
      })

    _promise.then(result => {
      _params.push(`key=${result.data}`)
      _params.push('type=1')
      _params.push('referer=myapp')
      src.value = `https://apis.map.qq.com/tools/locpicker?${_params.join('&')}`
    })

    break
}

const open = ref(false)
// 关闭时清理数据
watch(
  () => open.value,
  value => {
    if (value === false) {
      poi.value = null
    }
  }
)

const show = () => {
  open.value = true
}
defineExpose({
  show
})

const poi = ref(null)
window.addEventListener('message', function (e) {
  poi.value = {
    address: null,
    city: null,
    location: null,
    name: null
  }

  switch (props.platform) {
    case 'amap':
      poi.value.address = e.data.address

      // eslint-disable-next-line no-case-declarations
      const _array = poi.value.location && poi.value.location.split(',')
      if (Array.isArray(_array) && _array.length === 2) {
        poi.value.location = {
          lng: _array[0] - 0,
          lat: _array[1] - 0
        }
      }

      poi.value.name = e.data.name

      break
    case 'qq':
      poi.value.address = e.data.poiaddress
      poi.value.city = e.data.cityname
      poi.value.location = e.data.latlng
      poi.value.name = e.data.poiname

      break
  }
}, false)

const emits = defineEmits(['picked'])
const ok = () => {
  emits('picked', poi.value, props.id)

  open.value = false
}
</script>

<template>
  <a-modal
    v-model:open="open"
    :cancel-text="'取消'"
    :ok-text="'确定'"
    :centered="true"
    :closable="false"
    :destroy-on-close="true"
    :wrap-class-name="'full-screen'"
    :width="'90%'"
    @ok="ok"
  >
    <iframe
      :src="src"
      sandbox="allow-scripts allow-same-origin"
    />
  </a-modal>
</template>

<style lang="less">
  .full-screen {
    .ant-modal {
      max-width: 90%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(90vh);
    }

    .ant-modal-body {
      flex: 1;
    }
  }

  iframe {
    border: 1px solid #d9d9d9;
    height: 100%;
    width: 100%;
  }
</style>
