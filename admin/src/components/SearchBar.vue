<script setup>
import { nextTick, ref } from 'vue'
import { Grid } from 'ant-design-vue'
import jquery from 'jquery'
import GenericIcon from '@/components/GenericIcon.vue'
import StatefulButton from '@/components/StatefulButton.vue'

const props = defineProps({
  fields: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  actions: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  }
})

const gutter = [8, 8]

const span = {
  xs: 24,
  md: 8,
  xl: 4
}

const expand = ref(false)

const fields = ref([])
props.fields.forEach(i => {
  const _field = {
    ...i
  }

  const _options = {}

  switch (i.type) {
    case 'number':
      _options.min = null
      _options.max = null

      break
    case 'select':
      _options.mode = ''
      _options.options = {}

      break
  }

  jquery.extend(true, _options, i.options)
  _field.options = _options

  // 初始化取值
  if (!Object.prototype.hasOwnProperty.call(i, 'value')) {
    switch (i.type) {
      case 'department':
      case 'user':
        _field.value = []
        break
      case 'select':
        _field.value = i.mode === 'multiple' ? [] : null
        break
      default:
        _field.value = null
        break
    }
  }

  fields.value.push(_field)
})

const count = ref(0)
const breakpoint = Grid.useBreakpoint()
nextTick(() => {
  const _grid = 24

  if (breakpoint.value.xxxl || breakpoint.value.xxl || breakpoint.value.xl) {
    count.value = _grid / span.xl
  } else if (breakpoint.value.lg || breakpoint.value.md) {
    count.value = _grid / span.md
  } else {
    count.value = _grid / span.xs
  }
})

const model = () => {
  const _data = {}

  fields.value.forEach(i => {
    _data[i.field] = i.value
  })

  return _data
}
defineExpose({
  model
})

</script>

<template>
  <a-form class="layout-content-searchbar">
    <a-row :gutter="gutter">
      <template
        v-for="(i, index) in fields"
        :key="i"
      >
        <template v-if="index < count || expand">
          <a-col
            :md="span.md"
            :xl="span.xl"
            :xs="span.xs"
          >
            <a-form-item :name="index">
              <!-- 数字 -->
              <template v-if="i.type === 'number'">
                <a-input-number
                  v-model:value="i.value"
                  :max="i.options.max"
                  :min="i.options.min"
                >
                  <template #prefix>
                    <GenericIcon :icon="i.icon" />
                  </template>
                </a-input-number>
              </template>

              <!-- 时间范围 -->
              <template v-else-if="i.type === 'range'">
                <a-range-picker
                  v-model:value="i.value"
                  v-bind="i.config"
                >
                  <template
                    v-if="i.icon"
                    #suffixIcon
                  >
                    <GenericIcon :icon="i.icon" />
                  </template>
                </a-range-picker>
              </template>

              <!-- 下拉选框 -->
              <template v-else-if="i.type === 'select'">
                <a-select
                  v-model:value="i.value"
                  :allow-clear="true"
                  :options="i.config.options"
                  :placeholder="'请选择' + i.title"
                />
              </template>

              <!-- 默认为单行文本 -->
              <template v-else>
                <a-input
                  v-model:value="i.value"
                  :placeholder="i.title"
                >
                  <template #prefix>
                    <GenericIcon :icon="i.icon" />
                  </template>
                </a-input>
              </template>
            </a-form-item>
          </a-col>
        </template>
      </template>
      <a-col style="flex: auto;text-align: right;">
        <a-space>
          <!-- 高级搜索 -->
          <template v-if="fields.length > count">
            <a
              style="font-size: 12px"
              @click="expand = !expand"
            >
              <template v-if="expand">
                <up-outlined />
              </template>
              <template v-else>
                <down-outlined />
              </template>
              高级搜索
            </a>
          </template>

          <!-- ACTION-->
          <template
            v-for="(i, index) in props.actions"
            :key="index"
          >
            <StatefulButton
              :icon="i.icon"
              :type="'primary'"
              :on-click="i.callback"
            >
              {{ i.title }}
            </StatefulButton>
          </template>
        </a-space>
      </a-col>
    </a-row>
  </a-form>
</template>

<style scoped>
.ant-form-item {
  margin-bottom: 0;
}
</style>
