<script setup>
import GenericIcon from '@/components/GenericIcon.vue'

const props = defineProps({
  tip: {
    type: String,
    default: ''
  },
  questions: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['select'])
const click = item => {
  emits('select', item)
}
</script>

<template>
  <template v-if="props.questions.length > 0">
    <a-card
      :bordered="true"
      :size="'small'"
      style="width: fit-content"
    >
      <a-space :direction="'vertical'">
        <!-- 提示 -->
        <slot name="tip">
          <p style="margin: 0;">
            <a-space>
              <smile-outlined style="color: #fadb14;font-size: 22px" />
            </a-space>
          </p>
        </slot>

        <!-- 问题 -->
        <template
          v-for="(i, index) in props.questions"
          :key="index"
        >
          <a-button
            :type="'default'"
            :size="'large'"
            style="border: none"
            @click="click(i)"
          >
            <template #icon>
              <GenericIcon
                :icon="i.icon"
                style="color: #1890ff"
              />
            </template>
            {{ i.desc }}
          </a-button>
        </template>
      </a-space>
    </a-card>
  </template>
</template>

<style lang="less" scoped>
@import '@/less/default';
@import '@/global';

.ant-btn {
  background-color: @backgroup-color-base;
  font-size: @font-size-sm;
}

.ant-btn:hover {
  background-color: @backgroup-color-active;
  color: inherit;
}
</style>
