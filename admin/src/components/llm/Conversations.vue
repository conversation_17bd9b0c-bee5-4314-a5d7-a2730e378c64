<script setup>
import { onMounted, ref, watch } from 'vue'
import { Modal } from 'ant-design-vue'
import StatefulModal from '@/components/StatefulModal.vue'

const props = defineProps({
  conversation: {
    type: Object,
    default: () => {}
  },
  load: {
    type: Function,
    default: () => {
      return Promise.resolve([])
    }
  },
  rename: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: (item, tile) => Promise.reject()
  },
  remove: {
    type: Function,
    // eslint-disable-next-line prefer-promise-reject-errors
    default: item => Promise.reject()
  }
})

watch(
  () => props.conversation.id,
  val => {
    if (typeof val === 'string') {
      dates.value.forEach(i => {
        i.items.forEach(i => {
          i._selected = false
        })

        const _items = i.items.filter(j => j.id === val)
        if (_items.length > 0) {
          // 打开日期
          activeKeys.value = i.label

          _items[0]._selected = true
        }
      })
    }
  }
)

const activeKeys = ref([])

const dates = ref([])

watch(
  () => dates.value,
  val => {
    if (val.length > 0) {
      activeKeys.value = val[0].label
    }
  }
)

const load = () => {
  props.load().then(items => {
    dates.value = []

    const _today = items.filter(i => typeof i.createTime === 'string' && new Date().format('yyyy-MM-dd') === new Date(i.createTime).format('yyyy-MM-dd'))
    if (_today.length > 0) {
      dates.value.push({
        label: '今天',
        items: _today
      })
    }

    let _day = new Date().format('yyyy-MM-dd')
    _day = new Date(_day)
    _day = _day.addDays(-6)
    const _week = items.filter(i => typeof i.createTime === 'string' && _today.indexOf(i) === -1 && new Date(i.createTime) - _day >= 0)
    if (_week.length > 0) {
      dates.value.push({
        label: '最近7天',
        items: _week
      })
    }

    const _other = items.filter(i => _today.indexOf(i) === -1 && _week.indexOf(i) === -1)
    if (_other.length > 0) {
      dates.value.push({
        label: '其他',
        items: _other
      })
    }

    dates.value.forEach(i => {
      i.items.forEach(i => {
        i._selected = false
      })

      const _items = i.items.filter(j => j.id === props.conversation.id)
      if (_items.length > 0) {
        // 打开日期
        activeKeys.value = i.label

        _items[0]._selected = true
      }
    })
  })
}

// 重命名
const rename = item => {
  renameModel.value = {
    entity: item,
    title: item.title
  }

  modal.value.show()
}

const remove = item => {
  Modal.confirm({
    content: '您即将删除该对话，是否继续？',
    okType: 'danger',
    onOk: () => {
      const _promise = props.remove(item)
      _promise.then(() => {
        load()
      })

      return _promise
    }
  })
}

const modal = ref(null)

const closeModal = () => {
  renameModel.value = {
    id: '',
    title: ''
  }
}

const renameModel = ref({
  entity: null,
  title: ''
})

const onOK = () => {
  const _promise = props.rename(renameModel.value.entity, renameModel.value.title)
  _promise.then(() => {
    load()

    modal.value.close()
  })

  return _promise
}

const emits = defineEmits(['new', 'select'])

defineExpose({
  load
})

onMounted(() => {
  load()
})
</script>

<template>
  <a-button
    :type="'primary'"
    :block="true"
    @click="emits('new')"
  >
    新建对话
  </a-button>

  <a-collapse
    v-model:active-key="activeKeys"
    :bordered="false"
  >
    <template
      v-for="i in dates"
      :key="i.label"
    >
      <a-collapse-panel :header="i.label">
        <a-list
          :bordered="false"
          :data-source="i.items"
          :size="'small'"
          :split="false"
        >
          <template #renderItem="{ item }">
            <a-list-item
              :class="item._selected ? 'selected' : ''"
              @click="() => emits('select', item)"
            >
              <div style="flex: 1">
                {{ item.title || `未命名对话（${item.createTime}）` }}
              </div>
              <a-dropdown>
                <a-button
                  :type="'text'"
                  :shape="'circle'"
                  :size="'small'"
                >
                  <ellipsis-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      style="color: #1890ff"
                      @click.stop="rename(item)"
                    >
                      <edit-outlined />
                      重命名
                    </a-menu-item>
                    <a-menu-item
                      style="color: #f5222d"
                      @click.stop="remove(item)"
                    >
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-list-item>
          </template>
        </a-list>
      </a-collapse-panel>
    </template>
  </a-collapse>

  <!-- 重命名 -->
  <StatefulModal
    ref="modal"
    :buttons="[{
      title: '保存',
      icon: 'SaveOutlined',
      type: 'primary',
      onClick: onOK
    }]"
    :z-index="1001"
    @close="closeModal"
  >
    <a-form
      ref="form"
      v-model:model="renameModel"
    >
      <a-form-item
        :rules="[{
          required: true,
          message: '请输入标题'
        }]"
        name="title"
      >
        <a-input
          v-model:value="renameModel.title"
          placeholder="请输入标题"
        />
      </a-form-item>
    </a-form>
  </StatefulModal>
</template>

<style lang="less" scoped>
@import '@/less/default';

.ant-collapse {
  background-color: @white;
  padding-left: 0;
  padding-right: 0;

  :deep(.ant-collapse-header) {
    padding-left: 0;
    padding-right: 0;

    .ant-collapse-header-text {
      flex: unset;
    }
  }

  :deep(.ant-collapse-content-box) {
    padding: 0 !important;

    .ant-list-item.selected {
      background-color: #f0f0f0;
    }

    .ant-list-item:hover {
      background-color: #f0f0f0;
      cursor: pointer;
    }
  }
}
</style>
