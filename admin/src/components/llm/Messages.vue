<script setup>
import { ref } from 'vue'
import { marked } from 'marked'
import '@/utils/date.js'
import GenericIcon from '@/components/GenericIcon.vue'
import MediaApi from '@/api/media/record.js'
import FeedbackUtil from '@/utils/feedback.js'

const props = defineProps({
  avatar: {
    type: String,
    default: ''
  }
})

marked.setOptions({
  breaks: true, // 将换行符转换为 <br>
  gfm: true, // 启用 GitHub 风格 Markdown
  headerIds: false, // 不为标题生成 id
  mangle: false // 不转义内联的 HTML
})

const dataSource = ref([])

const STATUS = {
  RUNNING: 'running',
  SUCCESS: 'success',
  ABORTED: 'aborted',
  FAILED: 'failed'
}

const set = messages => {
  dataSource.value = messages.map(i => {
    const _message = {
      ...i
    }

    if (i.type === 'answer') {
      _message.documents = Array.isArray(i.documents) || []
      _message.links = Array.isArray(i.links) || []
    }

    return _message
  })
}

const ask = (message, media) => {
  const _message = {
    id: Date.now(),
    type: 'ask',
    mediaURL: null,
    content: message,
    timestamp: new Date(),
    status: STATUS.RUNNING
  }

  new Promise(resolve => {
    if (media instanceof File) {
      const _reader = new FileReader()
      _reader.onload = () => {
        _message.mediaURL = _reader.result

        resolve()
      }

      _reader.readAsDataURL(media)
    } else {
      resolve()
    }
  }).then(() => {
    dataSource.value.push(_message)
  })
  return {
    id: _message.id,
    onopen (event) {
      if (event.ok) {
        success(_message.id)
        _message._answerId = startAnswer(null)
      } else {
        fail(_message.id)
      }
    },
    onmessage ({ data }) {
      if (_message._answerId) {
        keepAnswer(_message._answerId, JSON.parse(data))
      }
    },
    onclose (event) {
      success(_message._answerId)
    },
    onerror (error) {
      // 将回答或提问设置为失败
      fail(_message._answerId || _message.id, error.message)
    }
  }
}

const startAnswer = () => {
  const _message = {
    id: Date.now(),
    type: 'answer',
    foldDocuments: true,
    foldLinks: true,
    foldReasoning: false,
    documents: [],
    links: [],
    reasoning: {
      description: '',
      duration: null
    },
    content: '',
    timestamp: new Date(),
    status: STATUS.RUNNING
  }

  dataSource.value.push(_message)

  return _message.id
}

const keepAnswer = (id, message) => {
  if (message == null) {
    return
  }

  dataSource.value
    .filter(i => i.id === id)
    .forEach(i => {
      if (Array.isArray(message.documents)) {
        i.documents = message.documents
      }

      if (Array.isArray(message.links)) {
        i.links = message.links
      }

      // 思考
      if (message.reasoning !== null) {
        if (message.reasoning.description !== null) {
          i.reasoning.description += message.reasoning.description
        }

        if (message.reasoning.duration !== null) {
          i.reasoning.duration = message.reasoning.duration
        }
      }

      // 回答
      if (typeof message.text === 'string') {
        i.content += message.text
      }
    })
}

const success = id => {
  dataSource.value.filter(i => i.id === id).forEach(i => {
    i.status = STATUS.SUCCESS
  })
}

const abort = id => {
  dataSource.value.filter(i => i.id === id).forEach(i => {
    i.status = STATUS.SUCCESS

    // 中止回答
    if (i._answerId) {
      dataSource.value.filter(j => i._answerId === j.id).forEach(j => {
        if (j.reasoning.duration === null) {
          j.reasoning.duration = new Date().getTime() - j.timestamp.getTime()
        }

        j.status = j.content.length === 0 ? STATUS.ABORTED : STATUS.SUCCESS
      })
    }
  })
}

const fail = (id, message) => {
  dataSource.value.filter(i => i.id === id).forEach(i => {
    i.status = STATUS.FAILED
    i._error = message
  })
}

defineExpose({
  set,
  ask,
  success,
  abort,
  fail
})

const copy = text => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      FeedbackUtil.message('复制成功', 'success')
    })
    .catch((error) => {
      console.error('Failed to copy text: ', error)
    })
}

const emits = defineEmits(['retry'])
const retry = message => {
  const _message = message.type === 'ask'
    ? [message]
    : dataSource.value.filter(i => i._answerId === message
      .id)
  if (_message.length > 0) {
    emits('retry', _message[0].content)
  }
}

</script>

<template>
  <div>
    <a-config-provider>
      <!-- 重置空状态 -->
      <template #renderEmpty />

      <a-list
        :item-layout="'horizontal'"
        :data-source="dataSource"
        :split="false"
      >
        <template #renderItem="{ item }">
          <a-list-item
            :style="{
              justifyContent: item.type === 'ask' ? 'right' : 'left'
            }"
          >
            <a-comment :class="item.type === 'ask' ? 'reverse' : ''">
              <template #avatar>
                <!-- 提问显示状态 -->
                <template v-if="item.type === 'ask'">
                  <template v-if="item.status === STATUS.RUNNING">
                    <a-avatar :size="46">
                      <template #icon>
                        <loading-outlined />
                      </template>
                    </a-avatar>
                  </template>
                  <template v-else-if="item.status === STATUS.FAILED">
                    <a-avatar :size="46">
                      <template #icon>
                        <a-tooltip :title="item._error">
                          <close-outlined />
                        </a-tooltip>
                      </template>
                    </a-avatar>
                  </template>
                </template>

                <!-- 答复显示头像 -->
                <template v-else>
                  <template v-if="item.avatar || props.avatar">
                    <a-avatar
                      :src="item.avatar || props.avatar"
                      :size="36"
                    />
                  </template>
                  <template v-else>
                    <a-avatar :size="36">
                      <template #icon>
                        <user-outlined />
                      </template>
                    </a-avatar>
                  </template>
                </template>
              </template>

              <!-- action -->
              <template #actions>
                <a-tooltip
                  :placement="'topLeft'"
                  :title="'复制'"
                >
                  <span>

                    <GenericIcon
                      :icon="'CopyOutlined'"
                      @click="copy(item.content)"
                    />
                  </span>
                </a-tooltip>
                <template v-if="(item.type === 'answer' && item.status !== STATUS.RUNNING) || item.status === STATUS.FAILED">
                  <a-tooltip
                    :placement="'topLeft'"
                    :title="item.type === 'ask' ? '重试' : '重新生成'"
                  >
                    <span>
                      <GenericIcon
                        :icon="'ReloadOutlined'"
                        @click="retry(item)"
                      />
                    </span>
                  </a-tooltip>
                </template>
              </template>

              <!-- 内容 -->
              <template #content>
                <!-- 回答前显示加载 -->
                <template v-if="item.type === 'answer' && item.status === STATUS.RUNNING && item.reasoning && item.reasoning.description.trim().length === 0 && (item.content == null || item.content.trim().length === 0) ">
                  <div style="text-align: center">
                    <loading-outlined />
                  </div>
                </template>
                <template v-else>
                  <!-- 提问 -->
                  <template v-if="item.type === 'ask'">
                    <template v-if="item.mediaURL !== null">
                      <a-image
                        :width="50"
                        :height="50"
                        :src="item.mediaURL"
                        :fallback="'data:image/png;base64,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'"
                      />
                    </template>
                    <p>{{ item.content }}</p>
                  </template>

                  <!-- 回答 -->
                  <template v-else>
                    <!-- RAG -->
                    <template v-if="item.documents.length > 0">
                      <a-typography-title
                        :level="5"
                        style="margin-bottom: 12px"
                      >
                        <a-space>
                          引用{{ item.documents.length }}篇本地资料作为参考

                          <!-- 折叠 -->
                          <template v-if="item.foldDocuments">
                            <down-outlined
                              style="color: #1890ff"
                              @click="item.foldDocuments = false"
                            />
                          </template>
                          <template v-else>
                            <up-outlined
                              style="color: #1890ff"
                              @click="item.foldDocuments = true"
                            />
                          </template>
                        </a-space>
                      </a-typography-title>

                      <template v-if="!item.foldDocuments">
                        <div style="margin: 12px 0">
                          <template v-for="(i, index) in item.documents">
                            <a-typography-paragraph :id="index">
                              <a
                                target="_blank"
                                @click="MediaApi.download(i.id)"
                              >
                                {{ i.name }}
                              </a>
                            </a-typography-paragraph>
                          </template>
                        </div>
                      </template>
                    </template>

                    <!-- 联网搜索 -->
                    <template v-if="item.links.length > 0">
                      <a-typography-title
                        :level="5"
                        style="margin-bottom: 12px"
                      >
                        <a-space>
                          引用{{ item.links.length }}篇网络资料作为参考

                          <!-- 折叠 -->
                          <template v-if="item.foldLinks">
                            <down-outlined
                              style="color: #1890ff"
                              @click="item.foldLinks = false"
                            />
                          </template>
                          <template v-else>
                            <up-outlined
                              style="color: #1890ff"
                              @click="item.foldLinks = true"
                            />
                          </template>
                        </a-space>
                      </a-typography-title>

                      <template v-if="!item.foldLinks">
                        <div style="margin: 12px 0">
                          <template v-for="(i, index) in item.links">
                            <a-typography-paragraph :id="index">
                              <a
                                :href="i.url"
                                target="_blank"
                              >
                                {{ i.title }}
                              </a>
                            </a-typography-paragraph>
                          </template>
                        </div>
                      </template>
                    </template>

                    <!-- 思考 -->
                    <template v-if="item.reasoning && item.reasoning.description.length > 0">
                      <a-typography-title
                        :level="5"
                        style="margin-bottom: 12px"
                      >
                        <a-space>
                          <template v-if="item.reasoning.duration === null">
                            思考中 <loading-outlined />
                          </template>
                          <template v-else>
                            已{{ item.status === STATUS.ABORTED ? '中止思考' : '思考完毕' }}（用时{{ item.reasoning.duration / 1000 }}秒）
                          </template>

                          <!-- 折叠 -->
                          <template v-if="item.foldReasoning">
                            <down-outlined
                              style="color: #1890ff"
                              @click="item.foldReasoning = false"
                            />
                          </template>
                          <template v-else>
                            <up-outlined
                              style="color: #1890ff"
                              @click="item.foldReasoning = true"
                            />
                          </template>
                        </a-space>
                      </a-typography-title>

                      <template v-if="!item.foldReasoning">
                        <a-typography-paragraph>
                          <blockquote>{{ item.reasoning.description }}</blockquote>
                        </a-typography-paragraph>
                      </template>
                    </template>

                    <div
                      class="markdown"
                      v-html="marked(item.content)"
                    />
                  </template>
                </template>
              </template>

              <!-- 时间 -->
              <template #datetime>
                <span>{{ item.timestamp.format('HH:mm:ss') }}</span>
              </template>
            </a-comment>
          </a-list-item>
        </template>
      </a-list>
    </a-config-provider>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';
@import '@/global';

.ant-list-item {
  padding: 0 0 12px 0;

  :deep(.ant-comment.reverse) {
    .ant-comment-avatar {
      margin-top: 30px;
      margin-right: 0;

      .ant-avatar {
        background-color: @white;
        font-size: @font-size-lg !important;

        .anticon.anticon-loading {
          color: #c0c0c0;
        }

        .anticon.anticon-close {
          color: @error-color;
        }
      }
    }
  }

  :deep(.ant-comment-inner) {
    padding: 0;

    .ant-comment-content-author {
      margin-bottom: @margin-sm;
    }

    .ant-comment-content-detail {
      border-radius: 16px;
      padding: @padding-sm;
      background-color: @backgroup-color-base;

      p {
        margin: 0;
      }

      .ant-typography {
        margin: 0;
        font-size: @font-size-sm !important;
        font-weight: unset;

        .ant-space {
          font-size: unset;
        }
      }

      .markdown {
        width: 100%;
        overflow-x: auto;

        p {
          margin: 0 0 0.5em 0;
        }

        p:last-child {
          margin-bottom: 0;
        }

        pre {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 8px;
          border-radius: 4px;
          overflow-x: auto;
        }

        code {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 2px 4px;
          border-radius: 3px;
        }

        ul, ol {
          margin: @margin-xss 0;
          padding-left: @padding-xss;
        }

        img {
          max-width: 100%;
        }

        blockquote {
          border-left: 4px solid #ddd;
          padding-left: 10px;
          color: #666;
          margin: 0.5em 0;
        }

        table {
          border-collapse: collapse;
          width: 100%;
          text-align: center;

          th, td {
            border: 1px solid #ddd;
            padding: @padding-xss;
          }

          th {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .ant-comment-actions {
      margin: 0;

      > li > span {
        margin-left: @margin-xs;
        margin-right: 0;
        font-size: @font-size-lg;
      }
    }
  }
}

.ant-list-item.reverse {
  justify-content: right;
}

.ant-list-item:last-of-type {
  padding: 0 12px 0 0;
}
</style>
