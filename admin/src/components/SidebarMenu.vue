<script setup>
import { getCurrentInstance, onMounted, ref, watch } from 'vue'
import GenericIcon from '@/components/GenericIcon.vue'
import SubMenu from '@/components/SubMenu.vue'
import AppApi from '@/api/sys/app.js'

const { proxy } = getCurrentInstance()

const props = defineProps({
  collapsed: Boolean,
  menu: {
    type: Promise,
    default: AppApi.getPages({
      showLoading: false,
      toast: {
        success: false
      }
    })
  }
})

const menu = ref({
  // 菜单树
  tree: [],
  // 子节点
  children: [],
  openKeys: [],
  selectedKeys: []
})

// 选中菜单项而打开菜单
const select = item => {
  const _path = setKeyAndPath(item.key, 'id')
  if (_path !== null) {
    proxy.$router.push({
      path: _path
    })
  }
}

const setKeyAndPath = (key, field) => {
  let _found = false

  let _key = null
  let _keyPath = []
  let _path = null

  // 自下而上搜索
  for (let i = menu.value.tree.length - 2; i >= 0; i--) {
    const _node = _found
      ? menu.value.tree[i + 1].filter(i => {
        return i.id === _keyPath[_keyPath.length - 1]
      })
      : menu.value.tree[i + 1].filter(i => {
        return i[field] === key
      })

    if (_node.length === 0) {
      continue
    }

    if (!_found) {
      _found = true

      _key = _node[0].id
      _keyPath.push(_node[0].id)
      _path = _node[0].path
    }

    for (let j = 0; j < menu.value.tree[i].length; j++) {
      if (menu.value.tree[i][j].id === _node[0].parentId) {
        _keyPath.push(menu.value.tree[i][j].id)

        break
      }
    }
  }

  // 在顶层匹配
  if (!_found) {
    for (let i = 0; i < menu.value.tree[0].length; i++) {
      if (key === menu.value.tree[0][i][field]) {
        _key = menu.value.tree[0][i].id
        _keyPath = [menu.value.tree[0][i].id]
        _path = menu.value.tree[0][i].path

        break
      }
    }
  }

  _keyPath.reverse()

  if (_key === null) {
    menu.value.selectedKeys = []
    menu.value.openKeys = []
  } else {
    menu.value.selectedKeys = [_key]
    menu.value.openKeys = _keyPath
  }

  return _path
}

// 路由改变时打开菜单
watch(
  () => proxy.$route,
  value => {
    setKeyAndPath(value.path, 'path')
  }
)

onMounted(() => {
  const _tree = []

  props.menu.then(result => {
    result.data.forEach(i => {
      // 设置默认图标
      if (!i.icon) {
        i.icon = 'MenuOutlined'
      }

      // 按级别分组
      const _lv = i.level
      if (_tree.length > _lv - 1 && Array.isArray(_tree[_lv - 1])) {
        _tree[_lv - 1].push(i)
      } else {
        _tree[_lv - 1] = [i]
      }
    })

    // 分组内排序
    for (let i = 0; i < _tree.length; i++) {
      _tree[i].sort(function (i, j) {
        return i.seq - j.seq
      })
    }

    _tree.push([])

    // 分组倒序排列，生成树状结构
    for (let i = _tree.length - 2; i >= 0; i--) {
      const _leaves = []

      for (let j = 0; j < _tree[i].length; j++) {
        _tree[i][j].subordinates = []

        _tree[i + 1].forEach(k => {
          if (_tree[i][j].id === k.parentId) {
            _tree[i][j].subordinates.push(k)
          }
        })

        // 不存在子菜单项
        if (_tree[i][j].subordinates.length === 0) {
          delete _tree[i][j].subordinates

          if (typeof _tree[i][j].path !== 'string') {
            _leaves.push(j)
          }
        }
      }

      // 删除未对应路由的菜单项
      for (let j = 0; j < _leaves.length; j++) {
        _tree[i].splice(_leaves[j] - j, 1)
      }
    }

    menu.value.tree = _tree
    menu.value.children = _tree[0]

    setKeyAndPath(proxy.$router.currentRoute.value.path, 'path')
  })
})
</script>

<template>
  <a-layout-sider
    :collapsed="props.collapsed"
    v-bind="$attrs"
  >
    <div class="layout-sider-logo">
      <img
        src="@/assets/logo.png"
        alt=""
      >
    </div>
    <a-menu
      v-model:open-keys="menu.openKeys"
      v-model:selected-keys="menu.selectedKeys"
      :mode="'inline'"
      :theme="'dark'"
      @select="select"
    >
      <template
        v-for="i in menu.children"
        :key="i.id"
      >
        <!-- 多级菜单 -->
        <template v-if="Array.isArray(i.subordinates) && i.subordinates.length > 0">
          <SubMenu :menu="i" />
        </template>

        <!-- 单级菜单 -->
        <template v-else>
          <a-menu-item :key="i.id">
            <!-- 图标 -->
            <template #icon>
              <GenericIcon :icon="i.icon" />
            </template>

            <!-- 标题 -->
            {{ i.title }}
          </a-menu-item>
        </template>
      </template>
    </a-menu>
  </a-layout-sider>
</template>

<style lang="less" scoped>
@import '@/less/default';

/* 大屏Logo */
@media (min-width: @screen-sm) {
  .layout-sider-logo {
    display: none;
  }
}

/* 小屏Logo */
@media (max-width: @screen-xs-max) {
  .layout-sider-logo img {
    margin: @margin-md;
    width: 168px;
  }
}
</style>
