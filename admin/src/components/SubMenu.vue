<script setup>
import GenericIcon from '@/components/GenericIcon.vue'

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop
  menu: {
    type: Object
  }
})
</script>

<template>
  <a-sub-menu :key="props.menu.id">
    <!-- 图标 -->
    <template #icon>
      <GenericIcon :icon="props.menu.icon" />
    </template>

    <!-- 标题 -->
    <template #title>
      {{ props.menu.title }}
    </template>

    <!-- 子菜单 -->
    <template v-if="Array.isArray(props.menu.subordinates) && props.menu.subordinates.length > 0">
      <template
        v-for="i in props.menu.subordinates"
        :key="i.id"
      >
        <template v-if="Array.isArray(i.subordinates) && i.subordinates.length > 0">
          <SubMenu
            :key="i.id"
            :menu="i"
          />
        </template>
        <template v-else>
          <a-menu-item :key="i.id">
            <!-- 图标 -->
            <template #icon>
              <GenericIcon :icon="i.icon" />
            </template>
            <!-- 标题 -->
            {{ i.title }}
          </a-menu-item>
        </template>
      </template>
    </template>
  </a-sub-menu>
</template>
