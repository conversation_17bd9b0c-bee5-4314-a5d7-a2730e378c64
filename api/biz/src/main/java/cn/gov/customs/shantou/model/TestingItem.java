package cn.gov.customs.shantou.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 检测项目
 */
@Entity
@Table(name = "testing_items")
public class TestingItem extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    // 项目名称
    @Column(nullable = false)
    @NotBlank
    String name;

    // 项目其他名称
    String alias;

    // 项目英文名称
    String englishName;

    // 项目拉丁名称
    String latinName;

    // 业务领域类别代码
    @Column(length = 16, nullable = false)
    @NotBlank
    String domainCode;

    // 生效时间
    Date effectiveTime;

    // 截止时间
    Date expirationTime;

    // 旧检测项目
    boolean outdated = false;

    // 是否启用
    boolean isEnabled = true;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String projectName) {
        this.name = StringUtils.trimWhitespace(projectName);
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = StringUtils.trimWhitespace(alias);
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = StringUtils.trimWhitespace(englishName);
    }

    public String getLatinName() {
        return latinName;
    }

    public void setLatinName(String latinName) {
        this.latinName = StringUtils.trimWhitespace(latinName);
    }

    public String getDomainCode() {
        return domainCode;
    }

    public void setDomainCode(String domainCode) {
        this.domainCode = StringUtils.trimWhitespace(domainCode);
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public boolean getOutdated() {
        return outdated;
    }

    public void setOutdated(boolean outdated) {
        this.outdated = outdated;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

}