package cn.gov.customs.shantou.repository;

import cn.gov.customs.shantou.model.TestingItem;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class TestingItemRepository extends AbstractEntityRepository<TestingItem> {

    public TestingItemRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, TestingItem.class);
    }

    @Transactional(readOnly = true)
    public Result<TestingItem> getById(String id) {
        Result<TestingItem> _record = new Result<>();

        if (!StringUtils.hasLength(id)) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{this.getClass().getSimpleName()});
            return _record;
        }

        _record.data = getCurrentSession().get(TestingItem.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{this.getClass().getSimpleName()});
        }

        return _record;
    }


    public PaginatedRecords<TestingItem> search(int count, int index, List<Sorter> sorters, String id, String name, String domainCode, Boolean outdated, Boolean isEnabled) {
        JinqStream<TestingItem> _query = stream(TestingItem.class);

        if (StringUtils.hasLength(id)) {
            _query = _query.where(i -> i.getId().contains(id));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        if (StringUtils.hasLength(domainCode)) {
            _query = _query.where(i -> domainCode.equals(i.getDomainCode()));
        }

        if (outdated != null) {
            _query = _query.where(i -> outdated.equals(i.getOutdated()));
        }

        // 按启用状态搜索
        if (isEnabled != null) {
            _query = _query.where(i -> isEnabled.equals(i.getIsEnabled()));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(TestingItem::getId);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<TestingItem> _records = new PaginatedRecords<>(count, index);
        _records.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _records.records = _query.toList();

        return _records;
    }

    public Result<String> save(TestingItem record, String operatorId) {
        record.setCreatorId(operatorId);
        record.setCreateTime(new Date());

        transactionTemplate.execute(status -> {
            entityManagerFactory.createEntityManager().persist(record);
            return null;
        });
    }

    /**
     * 更新检测项目
     *
     * @param record     检测项目
     * @param operatorId 操作人员ID
     */
    public void update(TestingItem record, String operatorId) {
        record.setMaintainerId(operatorId);
        record.setMaintainTime(new Date());

        transactionTemplate.execute(status -> {
            entityManagerFactory.createEntityManager().merge(record);
            return null;
        });
    }

    /**
     * 删除检测项目
     *
     * @param record 检测项目
     */
    public void remove(TestingItem record) {
        transactionTemplate.execute(status -> {
            entityManagerFactory.createEntityManager().remove(record);
            return null;
        });
    }

}