package cn.gov.customs.shantou.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 项目信息模型
 */
@Entity
@Table(name = "project", indexes = {
        @Index(columnList = "projectCode"),
        @Index(columnList = "businessDomainTypeCode"),
        @Index(columnList = "effectiveTime, expirationTime")
})
public class Project extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    // 项目代码
    @Column(length = 64, nullable = false, unique = true)
    @NotBlank
    @Size(max = 64)
    String projectCode;

    // 项目名称
    @Column(length = 128, nullable = false)
    @NotBlank
    @Size(max = 128)
    String projectName;

    // 项目其他名称
    @Column(length = 128)
    @Size(max = 128)
    String projectOtherName;

    // 项目英文名称
    @Column(length = 256)
    @Size(max = 256)
    String projectEnglishName;

    // 项目拉丁名称
    @Column(length = 256)
    @Size(max = 256)
    String projectLatinName;

    // 业务领域类别代码
    @Column(length = 32, nullable = false)
    @NotBlank
    @Size(max = 32)
    String businessDomainTypeCode;

    // 生效时间
    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    Date effectiveTime;

    // 截止时间
    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    Date expirationTime;

    // 是否启用
    boolean isEnabled = true;

    // 排序
    Integer seq = Integer.MAX_VALUE;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = StringUtils.trimWhitespace(projectCode);
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = StringUtils.trimWhitespace(projectName);
    }

    public String getProjectOtherName() {
        return projectOtherName;
    }

    public void setProjectOtherName(String projectOtherName) {
        this.projectOtherName = StringUtils.trimWhitespace(projectOtherName);
    }

    public String getProjectEnglishName() {
        return projectEnglishName;
    }

    public void setProjectEnglishName(String projectEnglishName) {
        this.projectEnglishName = StringUtils.trimWhitespace(projectEnglishName);
    }

    public String getProjectLatinName() {
        return projectLatinName;
    }

    public void setProjectLatinName(String projectLatinName) {
        this.projectLatinName = StringUtils.trimWhitespace(projectLatinName);
    }

    public String getBusinessDomainTypeCode() {
        return businessDomainTypeCode;
    }

    public void setBusinessDomainTypeCode(String businessDomainTypeCode) {
        this.businessDomainTypeCode = StringUtils.trimWhitespace(businessDomainTypeCode);
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public boolean getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(boolean enabled) {
        isEnabled = enabled;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

}