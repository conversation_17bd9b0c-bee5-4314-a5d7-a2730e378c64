<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60000" debug="false">
    <contextName>sparrow-webapi</contextName>

    <property name="common-pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] @%thread: %msg%n"></property>
    <property name="trace-pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] @%thread %class{0}.%method #%line: %msg%n"></property>
    <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"></property>
    <property name="url" value="*************************************************************************************************************************************************************************************"></property>
    <property name="username" value="gmccai"></property>
    <property name="password" value="LPDY!iLrUd8irpGp"></property>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
            <pattern>${common-pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="debug-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${trace-pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>log/debug(%d{yyyyMMdd})-%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <append>true</append>
    </appender>
    <appender name="debug-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="debug-appender"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="info-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${trace-pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>log/info(%d{yyyyMMdd})-%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <append>true</append>
    </appender>
    <appender name="info-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="info-appender"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="warn-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${trace-pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>log/warn(%d{yyyyMMdd})-%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <append>true</append>
    </appender>
    <appender name="warn-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="warn-appender"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="error-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${trace-pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>log/error(%d{yyyyMMdd})-%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <append>true</append>
    </appender>
    <appender name="error-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="error-appender"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="access-log-appender" class="com.chinamobile.sparrow.domain.infra.log.AccessLogAppender">
        <connectionSource class="ch.qos.logback.core.db.DataSourceConnectionSource">
            <dataSource class="com.zaxxer.hikari.HikariDataSource">
                <driverClassName>${driverClassName}</driverClassName>
                <jdbcUrl>${url}</jdbcUrl>
                <username>${username}</username>
                <password>${password}</password>
            </dataSource>
        </connectionSource>
    </appender>
    <appender name="access-log-async-appender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="access-log-appender"/>
    </appender>

    <root level="INFO" additivity="false">
        <appender-ref ref="stdout"/>
    </root>

    <logger name="com.chinamobile.sparrow.domain.infra.log.AutoLogAspect" level="INFO" additivity="false">
        <appender-ref ref="access-log-async-appender"/>
    </logger>

    <logger name="com.chinamobile" level="DEBUG" additivity="false">
        <appender-ref ref="debug-async-appender"/>
        <appender-ref ref="info-async-appender"/>
        <appender-ref ref="warn-async-appender"/>
        <appender-ref ref="error-async-appender"/>
    </logger>
</configuration>