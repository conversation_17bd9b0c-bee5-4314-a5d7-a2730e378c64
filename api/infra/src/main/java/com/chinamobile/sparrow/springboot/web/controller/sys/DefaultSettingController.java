package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "")
public class DefaultSettingController extends SettingController {

    public DefaultSettingController(String rsaPublicKey, UserRepository userRepository, DepartmentRepository departmentRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, PageRepository pageRepository, LoginUtil loginUtil) {
        super(rsaPublicKey, userRepository, departmentRepository, roleRepository, permissionRepository, pageRepository, loginUtil);
    }

    @Override
    public Result<Void> init() throws Exception {
        User _user = null;
        String _password = "********";

        if (userRepository.registeredUserNum() == 0) {
            // 新增部门
            Department _department = new Department();
            _department.setName("默认");
            _department.setCode(_department.getId());
            _department.setFullName(_department.getName());
            _department.setLevel(1);
            departmentRepository.add(_department, null);

            // 新增人员
            _user = new DefaultUser();
            _user.setAccount("admin");
            _user.setPassword(CryptoUtil.encryptRsa(_password, rsaPublicKey));
            _user.setName("系统管理员");
            _user.setDeptId(_department.getId());
            _user.setIsMale(true);
            userRepository.add(_user, null);
        }

        pageRepository.renew();

        if (!roleRepository.getByName("系统管理员").isOK()) {
            // 新增角色
            Role _role = new Role();
            _role.setName("系统管理员");
            roleRepository.add(_role, null);

            // 授权
            List<String> _permissionIds = permissionRepository.find(null).stream()
                    .filter(i -> i.getLevel() == 1)
                    .map(Permission::getId)
                    .collect(Collectors.toList());
            roleRepository.setPermissions(_role.getId(), _permissionIds, null);

            // 任命
            if (_user != null) {
                roleRepository.addUser(_role.getId(), _user.getId(), null);
            }
        }

        return new Result<>();
    }

}