package com.chinamobile.sparrow.springboot.web.controller.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Controller
public class DefaultReaderController extends ReaderController {

    public DefaultReaderController(AbstractMediaRepository mediaRepository, RecordController recordController, LoginUtil loginUtil) {
        super(mediaRepository, recordController, loginUtil);
    }

    @Override
    public ResponseEntity<StreamingResponseBody> readPart(HttpServletRequest request, @RequestParam(value = "id") String id) throws Exception {
        String _range = request.getHeader(HttpHeaders.RANGE);
        if (_range != null && _range.startsWith("bytes=")) {
            return writeToResponse(request, id);
        }

        Result<Media> _record = mediaRepository.get(id, null);
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        Result<Long> _length = mediaRepository.getContentLength(_record.data);
        if (!_length.isOK()) {
            throw new Exception(_length.message);
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                .header(HttpHeaders.ETAG, id)
                .header(HttpHeaders.LAST_MODIFIED, new Date().toString())
                .contentLength(_length.data)
                .body(null);
    }

    @Override
    public ResponseEntity<StreamingResponseBody> writeToResponse(HttpServletRequest request, String id) throws Exception {
        String _range = request.getHeader(HttpHeaders.RANGE);
        if (_range == null || !_range.startsWith("bytes=")) {
            return recordController.writeToResponse(id, false);
        }

        String[] _values = _range.split("=")[1].split("-");
        Long _start = StringUtils.hasLength(_values[0]) ? Long.parseLong(_values[0]) : 0L;
        Long _end = _values.length > 1 && StringUtils.hasLength(_values[1]) ? Long.parseLong(_values[1]) : null;

        Result<Media> _record = mediaRepository.get(id, null);
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        Result<Long> _length = mediaRepository.getContentLength(_record.data);
        if (!_length.isOK()) {
            throw new Exception(_length.message);
        }

        Result<byte[]> _bytes = mediaRepository.getPart(_record.data, _start, _end);
        if (!_bytes.isOK()) {
            throw new Exception(_bytes.message);
        }

        return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                .header(HttpHeaders.ACCEPT_RANGES, "bytes")
                .header(HttpHeaders.CONTENT_RANGE, String.format("bytes %s-%s/%s", _start, _start + _bytes.data.length - 1, _length.data))
                .header(HttpHeaders.ETAG, _record.data.getId())
                .header(HttpHeaders.LAST_MODIFIED, new Date().toString())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(_bytes.data.length)
                .body(output -> FileCopyUtils.copy(_bytes.data, output));
    }

}