package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.domain.model.sys.Page;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Map;

public class DefaultPageRepository extends PageRepository {

    public DefaultPageRepository(
            EntityManagerFactory entityManagerFactory,
            JinqJPAStreamProvider jinqJPAStreamProvider,
            PermissionRepository permissionRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Override
    public Map<String, String[]> readPagePermissionMappings() {
        Map<String, String[]> _maps = super.readPagePermissionMappings();

        // 大模型
        _maps.put("/llm/application/home", null);
        _maps.put("/llm/application/edit", null);

        _maps.put("/sys/llm/model", new String[]{"sys:llm:model:index"});
        _maps.put("/sys/llm/model/edit", new String[]{"sys:llm:model:edit"});

        return _maps;
    }

    @Override
    protected void customize(List<Page> records) {
        String _id = records.stream()
                .filter(i -> "大模型应用".equals(i.getTitle()) && 1 == i.getLevel())
                .map(Page::getId)
                .findFirst().orElse(null);
        if (_id == null) {
            _id = add("大模型应用", null, "icon-damoxing", 1, 2, true);
        }

        String _temp = _id;
        if (records.stream().noneMatch(i -> "对话".equals(i.getTitle()) && _temp.equals(i.getParentId()))) {
            add("对话", _id, "CommentOutlined", "/llm/conversation", 2, 1, true, false);
        }
        if (records.stream().noneMatch(i -> "工作流".equals(i.getTitle()) && _temp.equals(i.getParentId()))) {
            add("工作流", _id, "NodeIndexOutlined", "/llm/applications", 2, 2, true, false);
        }

        super.customize(records);
    }

    @Override
    protected void updateBuildIn(List<Page> records) {
        update(records, "/sys/llm/model", "系统管理", "大模型管理", "icon-damoxing", 2, 2);

        super.updateBuildIn(records);
    }

}