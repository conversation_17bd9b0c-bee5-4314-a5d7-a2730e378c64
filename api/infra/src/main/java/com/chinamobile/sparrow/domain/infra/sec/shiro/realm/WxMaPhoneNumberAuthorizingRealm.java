package com.chinamobile.sparrow.domain.infra.sec.shiro.realm;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxMaPhoneNumberToken;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.util.IdWorker;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.shiro.authc.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class WxMaPhoneNumberAuthorizingRealm extends AuthorizationRealm {

    final String DEPARTMENT_NAME = "小程序用户";

    final String departmentId;
    final AccessFacade accessFacade;

    public WxMaPhoneNumberAuthorizingRealm(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            AccessFacade accessFacade
    ) {
        super(null, userRepository, roleRepository, permissionRepository, null);

        List<Department> _departments = departmentRepository.find(null, Collections.singletonList(DEPARTMENT_NAME), null, true, null);
        if (CollectionUtils.isEmpty(_departments)) {
            Department _department = new Department();
            _department.setName(DEPARTMENT_NAME);
            _department.setCode(_department.getId());
            _department.setFullName(DEPARTMENT_NAME);
            _department.setLevel(1);
            departmentRepository.add(_department, null);

            this.departmentId = _department.getId();
        } else {
            this.departmentId = _departments.get(0).getId();
        }

        this.accessFacade = accessFacade;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof WxMaPhoneNumberToken;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        if (token.getPrincipal() == null || token.getCredentials() == null) {
            return null;
        }

        com.chinamobile.sparrow.domain.service.wx.ma.lang.User _dto;
        try {
            _dto = accessFacade.getUserWithDecryptedMp(new String((char[]) token.getCredentials()), ((WxMaPhoneNumberToken) token).getEncryptedData(), ((WxMaPhoneNumberToken) token).getIv());
        } catch (WxErrorException e) {
            logger.error("小程序授权码登录失败", e);

            throw new AuthenticationException(e);
        }

        Result<DefaultUser> _user = ((DefaultUserRepository) userRepository).getBriefInWxMa(_dto.openId, _dto.mp, true);
        if (_user.isOK()) {
            if (StringUtils.hasLength(_user.data.getMaOpenId()) && !Objects.equals(_dto.openId, _user.data.getMaOpenId())) {
                throw new AccountException("您的OpenId已被其他账号占用，登录失败");
            }

            if (!StringUtils.hasLength(_user.data.getMaOpenId())) {
                // 绑定微信小程序账号
                _user.data.setMaOpenId(_dto.openId);
                userRepository.update(_user.data, null);
            } else if (StringUtils.hasLength(_user.data.getMp()) && !Objects.equals(_dto.mp, _user.data.getMp())) {
                throw new AccountException("您的手机号码已被其他账号占用，登录失败");
            }
        }
        // 自动注册用户
        else {
            if (userRepository.getBriefByMp(_dto.mp, null).isOK()) {
                throw new AccountException("您的手机号码已被其他账号占用，登录失败");
            }


            DefaultUser _temp = new DefaultUser();
            _temp.setId(_dto.openId);
            _temp.setAccount(_dto.mp);
            _temp.setName(String.format("用户%s", IdWorker.getInstance().nextId()));
            _temp.setMp(_dto.mp);
            _temp.setDeptId(departmentId);
            _temp.setMaOpenId(_dto.openId);
            _temp.setUnionId(_dto.unionId);

            userRepository.add(_temp, null);

            _user.data = _temp;
        }

        return new SimpleAuthenticationInfo(_user.data, token.getCredentials(), this.getClass().toString());
    }

    @Override
    protected String getPrincipal(AuthenticationToken token) {
        return null;
    }

    @Override
    protected AuthenticationInfo getAuthenticationInfo(AuthenticationToken token, User user) {
        return null;
    }

}