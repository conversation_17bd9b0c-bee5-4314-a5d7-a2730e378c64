package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.DefaultUser;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "sys/user")
public class DefaultUserController extends UserController {

    public DefaultUserController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        super(userRepository, loginUtil);
    }

    @Override
    public Result<String> save(@RequestBody User record) {
        throw new UnsupportedOperationException();
    }

    @PostMapping(value = "/save/default")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:save")
    public Result<String> save(@RequestBody DefaultUser record) throws InstantiationException, IllegalAccessException {
        return userRepository.save(record, loginUtil.getUserId());
    }

}