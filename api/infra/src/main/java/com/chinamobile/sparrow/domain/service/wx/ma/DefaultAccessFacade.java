package com.chinamobile.sparrow.domain.service.wx.ma;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import me.chanjar.weixin.common.error.WxErrorException;

public class DefaultAccessFacade extends AccessFacade {

    public DefaultAccessFacade(WxMaService wxMaService) {
        super(wxMaService);
    }

    public WxMaJscode2SessionResult getSession(String code) throws WxErrorException {
        return wxMaService.getUserService().getSessionInfo(code);
    }

}