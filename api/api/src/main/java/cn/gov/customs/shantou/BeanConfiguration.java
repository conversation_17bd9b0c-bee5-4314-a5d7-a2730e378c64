package cn.gov.customs.shantou;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.chinamobile.sparrow.ai.infra.llm.plugin.IPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.ParserPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.WebSearchPlugin;
import com.chinamobile.sparrow.ai.model.llm.*;
import com.chinamobile.sparrow.ai.model.llm.Application;
import com.chinamobile.sparrow.ai.repository.llm.*;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.ai.service.dify.DatasetFacade;
import com.chinamobile.sparrow.ai.service.dify.LoginFacade;
import com.chinamobile.sparrow.ai.service.dify.infra.TokenStore;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.*;
import com.chinamobile.sparrow.domain.service.search.BingSearchFacade;
import com.chinamobile.sparrow.domain.service.search.IWebSearchService;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import com.chinamobile.sparrow.springboot.web.controller.ai.*;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultRecordController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import okhttp3.ConnectionPool;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import javax.persistence.EntityManagerFactory;
import java.util.Arrays;
import java.util.List;

@Configuration
public class BeanConfiguration {

    @Bean(value = "wxMaAccessFacade")
    @ConditionalOnProperty(value = "wx.ma.enabled", havingValue = "true")
    public AccessFacade accessFacade(WxMaService wxMaService) {
        return new DefaultAccessFacade(wxMaService);
    }

    @Bean
    @ConditionalOnMissingBean
    public TokenStore tokenStore(
            @Value(value = "${dify.base-url}") String baseUrl,
            @Value(value = "${dify.console.username}") String username,
            @Value(value = "${dify.console.password}") String password,
            ConnectionPool connectionPool) {
        return new TokenStore(new LoginFacade(baseUrl, username, password, connectionPool));
    }

    @Bean
    @ConditionalOnMissingBean
    public ConsoleFacade consoleFacade(
            @Value(value = "${dify.base-url}") String baseUrl,
            ConnectionPool connectionPool,
            TokenStore tokenStore) {
        return new ConsoleFacade(baseUrl, connectionPool, tokenStore);
    }

    @Bean
    @ConditionalOnMissingBean
    public DatasetFacade datasetFacade(
            @Value(value = "${dify.base-url}") String baseUrl,
            @Value(value = "${dify.api.key}") String apiKey,
            ConnectionPool connectionPool) {
        return new DatasetFacade(baseUrl, apiKey, connectionPool);
    }

    @Bean
    @ConditionalOnMissingBean(value = IWebSearchService.class)
    public IWebSearchService bingSearchFacade(
            @Value(value = "${bing.search.base-url}") String baseUrl,
            @Value(value = "${bing.search.api-key}") String subscriptionKey,
            ConnectionPool connectionPool) {
        return new BingSearchFacade(baseUrl, subscriptionKey, connectionPool);
    }

    @Bean
    @ConditionalOnMissingBean
    public List<IPlugin> plugins(
            @Value(value = "${bing.search.count}") int searchCount,
            @Lazy ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            IWebSearchService webSearchService) {
        return Arrays.asList(new ParserPlugin(conversationRepository, mediaRepository),
                new WebSearchPlugin(searchCount, webSearchService));
    }

    @Bean
    @ConditionalOnMissingBean
    public ModelRepository<?> modelRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${llm.model.debug}") boolean debug) {
        return new ModelRepository<>(entityManagerFactory, jinqJPAStreamProvider, Model.class, debug);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApplicationRepository<?> applicationRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            ConsoleFacade consoleFacade) {
        return new ApplicationRepository<>(entityManagerFactory, jinqJPAStreamProvider, Application.class,
                consoleFacade);
    }

    @Bean
    @ConditionalOnMissingBean
    public MemoryRepository<?> memoryRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${llm.memory.max-size}") Integer memorySize,
            @Value(value = "${llm.memory.redis.key-prefix}") String redisKeyPrefix,
            @Lazy ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            RedisConnectionFactory redisConnectionFactory) {
        return new MemoryRepository<>(entityManagerFactory, jinqJPAStreamProvider, Memory.class, memorySize,
                redisKeyPrefix, conversationRepository, mediaRepository, redisConnectionFactory);
    }

    @Bean
    @ConditionalOnMissingBean
    public DatasetRepository<?> datasetRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${spring.application.name}") String application,
            AbstractMediaRepository mediaRepository,
            DatasetFacade datasetFacade

    ) {
        return new DatasetRepository<>(entityManagerFactory, jinqJPAStreamProvider, Dataset.class, application,
                mediaRepository, datasetFacade);
    }

    @Bean
    @ConditionalOnMissingBean
    public ConversationRepository<?> conversationRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Value(value = "${spring.application.name}") String application,
            @Value(value = "${llm.conversation.multimodal-model}") String multimodalModel,
            @Value(value = "${llm.conversation.naming-model}") String namingModel,
            @Value(value = "${llm.memory.max-size}") Integer memorySize,
            @Value(value = "${llm.conversation.search.count}") int searchCount,
            @Value(value = "${dify.rag.retrieval-model}") String retrievalModel,
            @Value(value = "${dify.rag.retrieval-top-k}") Integer retrievalTopK,
            @Value(value = "${dify.rag.retrieval-score-threshold}") Double retrievalScore,
            @Value(value = "${dify.rag.reranking-mode}") String rerankingMode,
            @Value(value = "${dify.rag.reranking-model.name}") String rerankingModelName,
            @Value(value = "${dify.rag.reranking-model.provider}") String rerankingProviderName,
            ModelRepository<? extends Model> modelRepository,
            MemoryRepository<? extends Memory> memoryRepository,
            DatasetRepository<? extends Dataset> datasetRepository,
            AbstractMediaRepository mediaRepository,
            List<IPlugin> pluginImpls,
            IWebSearchService webSearchService,
            ConsoleFacade consoleFacade,
            DatasetFacade datasetFacade,
            ApplicationEventPublisher eventPublisher) {
        return new ConversationRepository<>(entityManagerFactory, jinqJPAStreamProvider, Conversation.class,
                application, multimodalModel, namingModel, memorySize, searchCount, retrievalModel, retrievalTopK,
                retrievalScore, rerankingMode, rerankingModelName, rerankingProviderName, null, null, modelRepository,
                memoryRepository, datasetRepository, mediaRepository, pluginImpls, webSearchService, consoleFacade,
                datasetFacade, eventPublisher);
    }

    @Bean
    public PageRepository pageRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            @Lazy PermissionRepository permissionRepository) {
        return new DefaultPageRepository(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Bean
    public UserRepository<?> userRepository(
            @Value(value = "${sec.password-constraint}") String passwordConstraint,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            ConversationRepository<? extends Conversation> conversationRepository,
            MemoryRepository<? extends Memory> memoryRepository,
            AbstractMediaRepository mediaRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade accessFacade) {
        return new DefaultUserRepository(entityManagerFactory, jinqJPAStreamProvider, conversationRepository,
                memoryRepository, mediaRepository, passwordConstraint, rsaPrivateKey, accessFacade);
    }

    @Bean
    public LoginController loginController(
            @Value(value = "${sec.captcha}") boolean captcha,
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            VerificationCodeRepository verificationCodeRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade,
            LoginUtil loginUtil) {
        return new DefaultLoginController(captcha, rsaPublicKey, rsaPrivateKey, verificationCodeRepository, null, null,
                null, wxMaAccessFacade, loginUtil);
    }

    @Bean
    public ProfileController profileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil) {
        return new DefaultProfileController(userRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ModelController modelController(
            ModelRepository<? extends Model> modelRepository,
            LoginUtil loginUtil) {
        return new ModelController(modelRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ConversationController conversationController(
            ConversationRepository<? extends Conversation> conversationRepository,
            MemoryRepository<? extends Memory> memoryRepository,
            LoginUtil loginUtil) {
        return new ConversationController(conversationRepository, memoryRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ApplicationController applicationController(
            ApplicationRepository<?> applicationRepository,
            LoginUtil loginUtil) {
        return new ApplicationController(applicationRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public DatasetController datasetController(
            @Value(value = "${llm.conversation.document.accept}") String accept,
            DatasetRepository<? extends Dataset> datasetRepository,
            LoginUtil loginUtil) {
        return new DatasetController(accept, datasetRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public DocumentController documentController(
            @Value(value = "${llm.conversation.document.accept}") String accept,
            ConversationRepository<? extends Conversation> conversationRepository,
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil) {
        return new DocumentController(accept, conversationRepository, mediaRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public PluginController pluginController(
            @Value(value = "${cognitive.speech.key}") String key,
            @Value(value = "${cognitive.speech.region}") String region,
            ConsoleFacade consoleFacade) {
        return new PluginController(key, region, consoleFacade);
    }

    @Bean
    public SettingController settingController(
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            DefaultUserRepository userRepository,
            DepartmentRepository<?> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            PageRepository pageRepository,
            LoginUtil loginUtil) {
        return new DefaultSettingController(rsaPublicKey, userRepository, departmentRepository, roleRepository,
                permissionRepository, pageRepository, loginUtil);
    }

    @Bean
    public ReaderController readerController(
            AbstractMediaRepository mediaRepository,
            RecordController recordController,
            LoginUtil loginUtil) {
        return new DefaultReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    public RecordController recordController(
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil) {
        return new DefaultRecordController(mediaRepository, loginUtil);
    }

    @Bean
    public UserController userController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil) {
        return new DefaultUserController(userRepository, loginUtil);
    }

}